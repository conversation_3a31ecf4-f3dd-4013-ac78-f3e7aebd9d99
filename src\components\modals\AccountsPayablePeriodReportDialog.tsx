import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api";
import { getPendingPayablesByPeriod, ConsolidatedPayable } from "@/api/financialReports";

// Mapeamento de nomes de cores para códigos HEX
const COLOR_MAP: Record<string, string> = {
  'red': '#E53E3E',    // Vermelho
  'blue': '#3182CE',   // Azul
  'purple': '#805AD5', // Roxo
  'green': '#38A169',  // Verde
  'yellow': '#D69E2E', // <PERSON><PERSON>
  'pink': '#D53F8C',  // Rosa
  'orange': '#DD6B20', // Laranja
  'teal': '#319795',   // Verde-água
  'gray': '#718096',   // Cinza
  'black': '#1A202C'   // Preto
};

// Função auxiliar para converter cor (HEX ou nome) para RGB
function hexToRgb(color: string | undefined | null) {
  // Se não houver cor definida ou for inválida, retorna azul padrão
  if (!color || typeof color !== 'string') {
    console.warn('Cor inválida, usando azul padrão');
    return { r: 41, g: 128, b: 185 };
  }
  
  try {
    // Verifica se é um nome de cor conhecido
    const hexColor = COLOR_MAP[color.toLowerCase()] || color;
    
    // Remove o # se presente
    const hexClean = hexColor.replace(/^#/, '');
    
    // Garante que o valor tem 6 caracteres
    const hexValid = hexClean.length === 3 
      ? hexClean.split('').map(c => c + c).join('') 
      : hexClean;
    
    // Converte para RGB
    const r = parseInt(hexValid.substring(0, 2), 16) || 0;
    const g = parseInt(hexValid.substring(2, 4), 16) || 0;
    const b = parseInt(hexValid.substring(4, 6), 16) || 0;
    
    // Valida os valores
    if (isNaN(r) || isNaN(g) || isNaN(b)) {
      throw new Error('Valores RGB inválidos');
    }
    
    return { r, g, b };
  } catch (error) {
    console.error('Erro ao converter cor:', color, error);
    return { r: 41, g: 128, b: 185 }; // Retorna azul padrão em caso de erro
  }
}
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface AccountsPayablePeriodReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Interface for jsPDF with autoTable
interface jsPDFWithAutoTable extends Omit<jsPDF, 'internal'> {
  autoTable: typeof autoTable;
  lastAutoTable?: any; // Adicionado para resolver o erro de tipagem
  internal: {
    getNumberOfPages: () => number;
    pageSize: {
      width: number;
      height: number;
      getWidth: () => number;
      getHeight: () => number;
    };
    scaleFactor: number;
    pages: number[];
    events: any;
    getEncryptor: (objectId: number) => (data: string) => string;
  };
}

export function AccountsPayablePeriodReportDialog({
  open,
  onOpenChange
}: AccountsPayablePeriodReportDialogProps) {
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [loading, setLoading] = useState(false);
  const clubId = useCurrentClubId();

  const handleGenerate = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado.",
        variant: "destructive",
      });
      return;
    }

    if (!startDate || !endDate) {
      toast({
        title: "Erro",
        description: "Por favor, selecione as datas de início e fim.",
        variant: "destructive",
      });
      return;
    }

    if (startDate > endDate) {
      toast({
        title: "Erro",
        description: "A data de início deve ser anterior à data de fim.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Get club info
      const clubInfo = await getClubInfo(clubId);

      // Get payables for the period
      const startDateStr = format(startDate, "yyyy-MM-dd");
      const endDateStr = format(endDate, "yyyy-MM-dd");
      const payables = await getPendingPayablesByPeriod(clubId, startDateStr, endDateStr);

      // Group by department
      const payablesByDepartment: Record<string, ConsolidatedPayable[]> = {};
      payables.forEach(payable => {
        const dept = payable.department || 'Outros';
        if (!payablesByDepartment[dept]) {
          payablesByDepartment[dept] = [];
        }
        payablesByDepartment[dept].push(payable);
      });

      // Create PDF
      const doc = new jsPDF("portrait", "mm", "a4") as jsPDFWithAutoTable;
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      const margin = 15;
      const headerHeight = 40;
      const logoSize = 30;
      const logoX = margin;
      const logoY = margin;

      // Definir cores do tema
      const primaryColor = hexToRgb(clubInfo.primary_color);
      const secondaryColor = clubInfo.secondary_color ? 
        hexToRgb(clubInfo.secondary_color) : 
        { r: Math.max(0, primaryColor.r - 30), g: Math.max(0, primaryColor.g - 30), b: Math.max(0, primaryColor.b - 30) };

      // Adicionar logo do clube se disponível
      if (clubInfo.logo_url) {
        try {
          const logoResponse = await fetch(clubInfo.logo_url);
          const logoBlob = await logoResponse.blob();
          const logoBase64 = await new Promise((resolve) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.readAsDataURL(logoBlob);
          });
          
          doc.addImage(logoBase64 as string, 'PNG', logoX, logoY, logoSize, logoSize);
        } catch (error) {
          console.error('Erro ao carregar o logo do clube:', error);
        }
      }

      // Cabeçalho
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(0, 0, 0);
      
      // Título do relatório
      const title = "Relatório de Contas a Pagar por Período";
      const titleX = pageWidth / 2;
      const titleY = logoY + 10;
      
      doc.text(title, titleX, titleY, { align: "center" });
      
      // Informações do clube
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text(clubInfo.name, titleX, titleY + 8, { align: "center" });
      
      if (clubInfo.address) {
        doc.setFontSize(10);
        doc.text(clubInfo.address, titleX, titleY + 14, { align: "center" });
      }

      // Linha divisória
      const lineY = logoY + logoSize + 5;
      doc.setDrawColor(200, 200, 200);
      doc.setLineWidth(0.5);
      doc.line(margin, lineY, pageWidth - margin, lineY);

      // Informações do período e data de geração
      const infoY = lineY + 10;
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100);
      doc.text(`Período: ${format(startDate, "dd/MM/yyyy", { locale: ptBR })} a ${format(endDate, "dd/MM/yyyy", { locale: ptBR })}`, margin, infoY);
      doc.text(`Gerado em: ${format(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}`, pageWidth - margin, infoY, { align: "right" });

      let currentY = infoY + 15;

      // Add summary
      const totalAmount = payables.reduce((sum, p) => sum + p.amount, 0);
      const totalCount = payables.length;

      doc.setFontSize(12);
      doc.text("Resumo do Período:", 15, currentY);
      currentY += 8;

      doc.setFontSize(10);
      doc.text(`Total de Contas: ${totalCount}`, 15, currentY);
      currentY += 5;
      doc.text(`Valor Total: R$ ${totalAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, currentY);
      currentY += 15;

      if (payables.length === 0) {
        doc.setFontSize(12);
        doc.text("Nenhuma conta a pagar encontrada para o período selecionado.", 15, currentY);
      } else {
        // Generate tables by department
        const sortedDepartments = Object.keys(payablesByDepartment).sort();

        for (const dept of sortedDepartments) {
          const deptPayables = payablesByDepartment[dept];
          const deptTotal = deptPayables.reduce((sum, p) => sum + p.amount, 0);

          // Cabeçalho do departamento
          doc.setFontSize(12);
          doc.setFont('helvetica', 'bold');
          
          // Fundo do cabeçalho
          doc.setFillColor(primaryColor.r, primaryColor.g, primaryColor.b);
          doc.rect(margin, currentY, pageWidth - (2 * margin), 8, 'F');
          
          // Texto do cabeçalho
          doc.setTextColor(255, 255, 255);
          const deptText = `${dept} (${deptPayables.length} itens - R$ ${deptTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`;
          doc.text(deptText, margin + 2, currentY + 5);
          
          doc.setTextColor(0, 0, 0);
          currentY += 12;

          // Prepare table data
          const tableData = deptPayables.map(payable => [
            payable.name,
            payable.description,
            format(new Date(payable.due_date || payable.transaction_date), "dd/MM/yyyy", { locale: ptBR }),
            payable.pix_key || 'Não informado',
            payable.role || '-',
            `R$ ${payable.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
          ]);

          // Add table
          autoTable(doc, {
            head: [["Nome", "Descrição", "Vencimento", "Chave PIX", "Função", "Valor"]],
            body: tableData,
            startY: currentY,
            styles: {
              fontSize: 8,
              cellPadding: 2,
            },
            headStyles: {
            fillColor: [secondaryColor.r, secondaryColor.g, secondaryColor.b] as [number, number, number],
            textColor: 255,
            fontStyle: 'bold' as const,
            halign: 'center'
          },
            alternateRowStyles: {
              fillColor: [240, 240, 240],
            },
            columnStyles: {
              5: { halign: 'right' }, // Valor
            },
            margin: { left: 15, right: 15 },
          });

          currentY = (doc as any).lastAutoTable.finalY + 10;

          // Check if we need a new page
          if (currentY > 250) {
            doc.addPage();
            currentY = 20;
          }
        }
      }

      // Add page numbers
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(`Página ${i} de ${pageCount}`, pageWidth / 2, doc.internal.pageSize.height - 10, { align: "center" });
      }

      // Save the PDF
      const reportTitle = `Contas_a_Pagar_${format(startDate, "dd-MM-yyyy")}_a_${format(endDate, "dd-MM-yyyy")}.pdf`;
      doc.save(reportTitle);

      toast({
        title: "Relatório gerado",
        description: "O relatório de contas a pagar por período foi gerado com sucesso.",
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o relatório.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Relatório de Contas a Pagar por Período</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Data de Início</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => date && setStartDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div>
              <Label htmlFor="endDate">Data de Fim</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => date && setEndDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? "Gerando..." : "Gerar Relatório"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
