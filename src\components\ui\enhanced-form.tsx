import React from "react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RequiredIndicator } from "./required-indicator";

interface FormFieldProps {
  label: string;
  required?: boolean;
  error?: string;
  children: React.ReactNode;
  className?: string;
  description?: string;
}

export function FormField({ 
  label, 
  required = false, 
  error, 
  children, 
  className,
  description 
}: FormFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <Label className="text-sm font-medium">
        {label}
        {required && <RequiredIndicator />}
      </Label>
      {children}
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      {error && (
        <p className="text-xs text-destructive font-medium">{error}</p>
      )}
    </div>
  );
}

interface EnhancedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  required?: boolean;
  error?: string;
  description?: string;
  containerClassName?: string;
}

export function EnhancedInput({ 
  label, 
  required = false, 
  error, 
  description,
  containerClassName,
  className,
  ...props 
}: EnhancedInputProps) {
  return (
    <FormField 
      label={label} 
      required={required} 
      error={error} 
      description={description}
      className={containerClassName}
    >
      <Input 
        className={cn(
          error && "border-destructive focus-visible:ring-destructive",
          className
        )}
        {...props} 
      />
    </FormField>
  );
}

interface EnhancedTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  required?: boolean;
  error?: string;
  description?: string;
  containerClassName?: string;
}

export function EnhancedTextarea({ 
  label, 
  required = false, 
  error, 
  description,
  containerClassName,
  className,
  ...props 
}: EnhancedTextareaProps) {
  return (
    <FormField 
      label={label} 
      required={required} 
      error={error} 
      description={description}
      className={containerClassName}
    >
      <Textarea 
        className={cn(
          error && "border-destructive focus-visible:ring-destructive",
          className
        )}
        {...props} 
      />
    </FormField>
  );
}

interface EnhancedSelectProps {
  label: string;
  required?: boolean;
  error?: string;
  description?: string;
  containerClassName?: string;
  placeholder?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  children: React.ReactNode;
}

export function EnhancedSelect({ 
  label, 
  required = false, 
  error, 
  description,
  containerClassName,
  placeholder,
  value,
  onValueChange,
  children
}: EnhancedSelectProps) {
  return (
    <FormField 
      label={label} 
      required={required} 
      error={error} 
      description={description}
      className={containerClassName}
    >
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger className={cn(
          error && "border-destructive focus:ring-destructive"
        )}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {children}
        </SelectContent>
      </Select>
    </FormField>
  );
}

interface FormSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export function FormSection({ title, description, children, className }: FormSectionProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="space-y-1">
        <h3 className="text-lg font-semibold text-primary">{title}</h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}

interface FormGridProps {
  children: React.ReactNode;
  cols?: 1 | 2 | 3 | 4;
  className?: string;
}

export function FormGrid({ children, cols = 2, className }: FormGridProps) {
  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
  };

  return (
    <div className={cn("grid gap-4", gridCols[cols], className)}>
      {children}
    </div>
  );
}
