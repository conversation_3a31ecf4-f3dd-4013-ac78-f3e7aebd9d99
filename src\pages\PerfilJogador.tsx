import { useEffect, useState, useRef } from "react";
import { useParams } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { usePlayerProfileStore } from "@/store/usePlayerProfileStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { Player, getPlayerUpcomingMatches, getPlayerUpcomingCallups, type UpcomingMatch, type Callup } from "@/api/api";
import { generateCallupPDF } from "@/api/callups";
import { Badge } from "@/components/ui/badge";
import { useTrainingsStore } from "@/store/useTrainingsStore";
import { PlayerMedicalHistory } from "@/components/player/PlayerMedicalHistory";
import { PlayerDocuments } from "@/components/player/PlayerDocuments";
import { PlayerFinances } from "@/components/player/PlayerFinances";
import { PlayerEvaluation } from "@/components/player/PlayerEvaluation";
import { PlayerAccountForm } from "@/components/player/PlayerAccountForm";
import { PlayerSelfEditDialog } from "@/components/modals/PlayerSelfEditDialog";
import { PlayerStatisticsPreview } from "@/components/player/PlayerStatisticsPreview";
import { TrainingImages } from "@/components/training/TrainingImages";
import { FileText, DollarSign, UserPlus, Edit, ClipboardCheck, Camera, Calendar, Users } from "lucide-react";
import { usePermission } from "@/hooks/usePermission";
import { PermissionControl } from "@/components/PermissionControl";
import { useUser } from "@/context/UserContext";
import { isPlayerOwnedByUser, uploadPlayerPhoto } from "@/api/players";

const statusColors = {
  ativo: "bg-green-100 text-green-800 border-green-200",
  lesionado: "bg-red-100 text-red-800 border-red-200",
  suspenso: "bg-amber-100 text-amber-800 border-amber-200",
  emprestado: "bg-purple-100 text-purple-800 border-purple-200",
  férias: "bg-primary/10 text-primary border-primary/20",
};

export default function PerfilJogador() {
  const { id } = useParams();
  const clubId = useCurrentClubId();
  const { player, loading, error, fetchPlayerProfile } = usePlayerProfileStore();
  const { toast } = useToast();
  const { user } = useUser();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Stores para histórico médico e sessões/treinamentos
  const { trainings, fetchTrainings } = useTrainingsStore();

  // Estados para controle de permissões e visualização de documentos
  const [canEditDocuments, setCanEditDocuments] = useState(false);
  const [canViewFinances, setCanViewFinances] = useState(false);
  const [canCreateAccount, setCanCreateAccount] = useState(false);
  const [activeTab, setActiveTab] = useState("info");
  const [isAccountFormOpen, setIsAccountFormOpen] = useState(false);
  const [isEditProfileOpen, setIsEditProfileOpen] = useState(false);
  const [isOwnProfile, setIsOwnProfile] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);

  // Estados para partidas e convocações
  const [upcomingMatches, setUpcomingMatches] = useState<UpcomingMatch[]>([]);
  const [upcomingCallups, setUpcomingCallups] = useState<Callup[]>([]);
  const [loadingMatches, setLoadingMatches] = useState(false);
  const [loadingCallups, setLoadingCallups] = useState(false);
  const [generatingPDF, setGeneratingPDF] = useState(false);

  useEffect(() => {
    if (id) {
      fetchPlayerProfile(clubId, id);
      fetchTrainings(clubId);
    }
  }, [clubId, id, fetchPlayerProfile, fetchTrainings]);

  // Buscar partidas e convocações do jogador
  useEffect(() => {
    if (id && clubId) {
      // Buscar partidas futuras
      setLoadingMatches(true);
      getPlayerUpcomingMatches(clubId, id)
        .then(setUpcomingMatches)
        .catch(error => {
          console.error("Erro ao buscar partidas do jogador:", error);
          setUpcomingMatches([]);
        })
        .finally(() => setLoadingMatches(false));

      // Buscar convocações futuras
      setLoadingCallups(true);
      getPlayerUpcomingCallups(clubId, id)
        .then(setUpcomingCallups)
        .catch(error => {
          console.error("Erro ao buscar convocações do jogador:", error);
          setUpcomingCallups([]);
        })
        .finally(() => setLoadingCallups(false));
    }
  }, [clubId, id]);

  // Função para gerar PDF da convocação
  const handleGenerateCallupPDF = async (callupId: number) => {
    try {
      setGeneratingPDF(true);
      await generateCallupPDF(clubId, callupId, false);
      toast({
        title: "Sucesso",
        description: "PDF da convocação gerado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar PDF da convocação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar o PDF da convocação.",
        variant: "destructive",
      });
    } finally {
      setGeneratingPDF(false);
    }
  };

  // Usar o hook de permissões
  const { can, role } = usePermission();

  // Verificar se é o próprio perfil do jogador
  useEffect(() => {
    const checkIfOwnProfile = async () => {
      if (id && user?.id) {
        try {
          const result = await isPlayerOwnedByUser(clubId, id, user.id);
          setIsOwnProfile(result);
        } catch (error) {
          console.error("Erro ao verificar propriedade do perfil:", error);
          setIsOwnProfile(false);
        }
      }
    };

    checkIfOwnProfile();
  }, [id, user?.id, clubId]);

  // Verificar permissões do usuário
  useEffect(() => {
    setCanEditDocuments(can("players.documents.verify"));
    setCanViewFinances(can("players.finances.view"));
    setCanCreateAccount(can("players.create") && can("users.create"));
  }, [can]);

  // Função para fazer upload da foto do jogador
  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || !event.target.files[0] || !id) return;

    const file = event.target.files[0];

    try {
      setUploadingPhoto(true);

      // Verificar se o arquivo é uma imagem
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Erro",
          description: "O arquivo deve ser uma imagem",
          variant: "destructive"
        });
        return;
      }

      // Verificar tamanho do arquivo (máximo 2MB)
      const MAX_SIZE = 2 * 1024 * 1024; // 2MB
      if (file.size > MAX_SIZE) {
        toast({
          title: "Erro",
          description: "A imagem deve ter no máximo 2MB",
          variant: "destructive"
        });
        return;
      }

      // Fazer upload da foto
      const userId = user?.id;
      const imageUrl = await uploadPlayerPhoto(clubId, id, file, userId);

      // Atualizar o perfil do jogador
      await fetchPlayerProfile(clubId, id);

      toast({
        title: "Sucesso",
        description: "Foto atualizada com sucesso",
      });
    } catch (error: any) {
      console.error("Erro ao fazer upload da foto:", error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao fazer upload da foto",
        variant: "destructive"
      });
    } finally {
      setUploadingPhoto(false);
      // Limpar o input de arquivo
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  useEffect(() => {
    if (error) {
      toast({
        title: "Erro ao carregar perfil",
        description: error,
        variant: "destructive"
      });
    }
  }, [error, toast]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-80">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
      </div>
    );
  }

  if (!player) return null;

  const getStatusColor = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800 border-gray-200";
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{player.name}</h1>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline" className={getStatusColor(player.status)}>
              {player.status}
            </Badge>
            <span className="text-muted-foreground">{player.position}</span>

            {/* Botão para criar conta (apenas para admins) */}
            <PermissionControl
              permissions={["players.create", "users.create"]}
              requireAll={true}
            >
              {!player.user_id && (
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-2"
                  onClick={() => setIsAccountFormOpen(true)}
                >
                  <UserPlus className="h-4 w-4 mr-1" />
                  Criar Conta
                </Button>
              )}
            </PermissionControl>

            {/* Botão para editar próprio perfil (apenas para jogadores) */}
            {isOwnProfile && (
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={() => setIsEditProfileOpen(true)}
              >
                <Edit className="h-4 w-4 mr-1" />
                Editar Perfil
              </Button>
            )}

            {player.user_id && (
              <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                Conta Vinculada
              </Badge>
            )}
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative group">
            <div className="h-20 w-20 bg-muted rounded-full overflow-hidden">
              {player.image ? (
                <img
                  src={player.image}
                  alt={player.name}
                  className="h-full w-full object-cover"
                />
              ) : (
                <div className="h-full w-full flex items-center justify-center bg-team-blue/10 text-team-blue text-2xl font-bold">
                  {player.name?.charAt(0)}
                </div>
              )}
            </div>

            {/* Botão para upload de foto (visível apenas para o próprio jogador ou administradores) */}
            {(isOwnProfile || can("players.edit")) && (
              <>
                <input
                  type="file"
                  ref={fileInputRef}
                  accept="image/*"
                  className="hidden"
                  onChange={handlePhotoUpload}
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploadingPhoto}
                  className="absolute bottom-0 right-0 bg-team-blue text-white rounded-full p-1 shadow-md hover:bg-team-blue/80 transition-colors"
                  title="Atualizar foto"
                >
                  {uploadingPhoto ? (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  ) : (
                    <Camera className="h-4 w-4" />
                  )}
                </button>
              </>
            )}
          </div>
          <div className="flex flex-col items-center justify-center">
            <span className="text-4xl font-bold text-team-blue">{player.number}</span>
            <span className="text-xs text-muted-foreground uppercase">Camisa</span>
          </div>
        </div>
      </div>

      <Separator />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="info">Informações</TabsTrigger>

          <PermissionControl permissions={["medical.view", "players.view_own"]}>
            <TabsTrigger value="medical">Histórico Médico</TabsTrigger>
          </PermissionControl>

          <PermissionControl permissions={["players.documents.view", "players.view_own"]}>
            <TabsTrigger value="documents" className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              <span>Documentos</span>
              <PermissionControl permission="players.documents.verify">
                <span className="ml-1 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full">
                  !
                </span>
              </PermissionControl>
            </TabsTrigger>
          </PermissionControl>

          <PermissionControl permission="players.finances.view">
            <TabsTrigger value="finances" className="flex items-center gap-1">
              <DollarSign className="h-4 w-4" />
              <span>Finanças</span>
            </TabsTrigger>
          </PermissionControl>

          <PermissionControl permissions={["players.edit", "players.view_own"]}>
            <TabsTrigger value="evaluation" className="flex items-center gap-1">
              <ClipboardCheck className="h-4 w-4" />
              <span>Avaliação do Atleta</span>
            </TabsTrigger>
          </PermissionControl>
        </TabsList>

        <TabsContent value="info">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Informações Pessoais</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Nome</p>
                <p className="font-medium">{player.name}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Apelido</p>
                <p className="font-medium">{player.nickname || "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Idade</p>
                <p className="font-medium">{player.age} anos</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Nacionalidade</p>
                <p className="font-medium">{player.nationality || "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Altura</p>
                <p className="font-medium">{player.height ? `${player.height} cm` : "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Peso</p>
                <p className="font-medium">{player.weight ? `${player.weight} kg` : "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Data de Nascimento</p>
                <p className="font-medium">{player.birthdate ? new Date(player.birthdate).toLocaleDateString() : "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Local de Nascimento</p>
                <p className="font-medium">{player.birthplace || "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <p className="font-medium">{player.status === "disponivel" ? "Disponível" :
                                           player.status === "lesionado" ? "Lesionado" :
                                           player.status === "suspenso" ? "Suspenso" :
                                           player.status === "emprestado" ? "Emprestado" : player.status}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status Profissional</p>
                <p className="font-medium">{player.professional_status === "profissional" ? "Profissional" :
                                           player.professional_status === "amador" ? "Amador" :
                                           "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">No clube desde</p>
                <p className="font-medium">{player.entry_date ? new Date(player.entry_date).toLocaleDateString() : "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">CPF</p>
                <p className="font-medium">{player.cpf_number || "Não informado"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Estatísticas</CardTitle>
            <Button
              variant="link"
              onClick={() => window.location.href = `/jogador/${player.id}/estatisticas`}
              className="text-team-blue"
            >
              Ver detalhes
            </Button>
          </CardHeader>
          <CardContent>
            <PlayerStatisticsPreview playerId={player.id} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Informações de Contato</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Telefone</p>
                <p className="font-medium">{player.phone || "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Email</p>
                <p className="font-medium">{player.email || "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Endereço</p>
                <p className="font-medium">{player.address || "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">CEP</p>
                <p className="font-medium">{player.zip_code || "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Cidade</p>
                <p className="font-medium">{player.city || "Não informado"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Estado</p>
                <p className="font-medium">{player.state || "Não informado"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Histórico Médico</CardTitle>
            <Button
              variant="link"
              onClick={() => setActiveTab("medical")}
              className="text-team-blue"
            >
              Ver detalhes
            </Button>
          </CardHeader>
          <CardContent>
            <div className="text-center py-4">
              <Button
                variant="outline"
                onClick={() => setActiveTab("medical")}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Ver histórico médico completo
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Próximos Treinamentos</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              {trainings
                .filter(t => {
                  // Verificar se o jogador está associado ao treino
                  if (!t.player_ids || !Array.isArray(t.player_ids) || !t.player_ids.includes(player.id)) {
                    return false;
                  }

                  // Filtrar apenas treinos futuros
                  const now = new Date();
                  const trainingDateTime = new Date(`${t.date}T${t.start_time || '00:00'}`);
                  return trainingDateTime >= now;
                })
                .sort((a, b) => {
                  // Ordenar por data (mais próximos primeiro)
                  const dateA = new Date(`${a.date}T${a.start_time || '00:00'}`);
                  const dateB = new Date(`${b.date}T${b.start_time || '00:00'}`);
                  return dateA.getTime() - dateB.getTime();
                })
                .slice(0, 3) // Mostrar apenas os 3 próximos treinamentos
                .map(training => (
                  <div key={training.id} className="border rounded-lg p-4 mb-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{training.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {training.date}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-x-4 gap-y-1 mt-2 text-xs text-muted-foreground">
                      <div>Horário: {training.start_time}{training.end_time ? ` - ${training.end_time}` : ''}</div>
                      <div>Local: {training.location}</div>
                      <div>Tipo: {training.type}</div>
                      {training.coach && <div>Treinador: {training.coach}</div>}
                    </div>
                    {training.description && <div className="text-sm mt-2">{training.description}</div>}
                    {training.images && training.images.length > 0 && (
                      <TrainingImages images={training.images} maxDisplay={2} />
                    )}
                  </div>
                ))}
              {trainings.filter(t => t.player_ids && Array.isArray(t.player_ids) && t.player_ids.includes(player.id)).length === 0 && (
                <div className="text-muted-foreground text-sm">Nenhum treinamento encontrado.</div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-1">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Próximas Partidas
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              {loadingMatches ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-team-blue mx-auto"></div>
                </div>
              ) : upcomingMatches.length > 0 ? (
                upcomingMatches.slice(0, 3).map(match => (
                  <div key={match.id} className="border rounded-lg p-4 mb-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">vs. {match.opponent}</span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(match.date).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-x-4 gap-y-1 mt-2 text-xs text-muted-foreground">
                      <div>Horário: {match.time}</div>
                      <div>Local: {match.location}</div>
                      <div>Competição: {match.competition}</div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-muted-foreground text-sm">Nenhuma partida agendada.</div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-1">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Convocações
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              {loadingCallups ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-team-blue mx-auto"></div>
                </div>
              ) : upcomingCallups.length > 0 ? (
                upcomingCallups.slice(0, 3).map(callup => (
                  <div key={callup.id} className="border rounded-lg p-4 mb-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{callup.tournament_type}</span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(callup.match_date).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-x-4 gap-y-1 mt-2 text-xs text-muted-foreground">
                      <div>Local: {callup.match_location}</div>
                      {callup.categories && (
                        <div>Categoria: {callup.categories.name}</div>
                      )}
                    </div>
                    <div className="flex justify-end mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleGenerateCallupPDF(callup.id)}
                        disabled={generatingPDF}
                        className="text-xs"
                      >
                        <FileText className="h-3 w-3 mr-1" />
                        {generatingPDF ? "Gerando..." : "Ver PDF"}
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-muted-foreground text-sm">Nenhuma convocação encontrada.</div>
              )}
            </div>
          </CardContent>
        </Card>
          </div>
        </TabsContent>

        <TabsContent value="medical">
          <PermissionControl permissions={["medical.view", "players.view_own"]}>
            <Card className="md:col-span-2">
              <CardContent className="pt-6">
                {player && <PlayerMedicalHistory player={player} />}
              </CardContent>
            </Card>
          </PermissionControl>

          <PermissionControl permissions={["medical.view", "players.view_own"]}>
            <Card className="md:col-span-2 mt-6">
              <CardHeader>
                <CardTitle>Treinamentos</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <h4 className="font-medium mb-2">Próximos Treinamentos</h4>
                  {trainings
                    .filter(t => {
                      // Verificar se o jogador está associado ao treino através da tabela training_players
                      // Esta verificação será feita pelo backend na função getTrainings
                      if (!t.player_ids || !Array.isArray(t.player_ids) || !t.player_ids.includes(player.id)) {
                        return false;
                      }

                      // Filtrar apenas treinos futuros e não concluídos
                      const now = new Date();
                      const trainingDateTime = new Date(`${t.date}T${t.start_time || '00:00'}`);
                      return trainingDateTime >= now && t.status !== "concluído";
                    })
                    .sort((a, b) => {
                      // Ordenar por data (mais próximos primeiro)
                      const dateA = new Date(`${a.date}T${a.start_time || '00:00'}`);
                      const dateB = new Date(`${b.date}T${b.start_time || '00:00'}`);
                      return dateA.getTime() - dateB.getTime();
                    })
                    .map(training => (
                      <div key={training.id} className="border rounded-lg p-4 mb-2">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{training.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {training.date}
                          </span>
                        </div>
                        <div className="flex flex-wrap gap-x-4 gap-y-1 mt-2 text-xs text-muted-foreground">
                          <div>Horário: {training.start_time}{training.end_time ? ` - ${training.end_time}` : ''}</div>
                          <div>Local: {training.location}</div>
                          <div>Tipo: {training.type}</div>
                          {training.coach && <div>Treinador: {training.coach}</div>}
                        </div>
                        {training.description && <div className="text-sm mt-2">{training.description}</div>}
                        {training.images && training.images.length > 0 && (
                          <TrainingImages images={training.images} maxDisplay={3} />
                        )}
                      </div>
                    ))}
                  {trainings.filter(t => t.player_ids && Array.isArray(t.player_ids) && t.player_ids.includes(player.id)).length === 0 && (
                    <div className="text-muted-foreground text-sm">Nenhum treinamento encontrado.</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </PermissionControl>
        </TabsContent>

        <TabsContent value="documents">
          <PermissionControl permissions={["players.documents.view", "players.view_own"]}>
            {id && <PlayerDocuments playerId={id} canEdit={can("players.documents.verify") || can("players.view_own")} />}
          </PermissionControl>
        </TabsContent>

        <TabsContent value="finances">
          <PermissionControl permission="players.finances.view">
            {id && <PlayerFinances playerId={id} canEdit={can("players.finances.edit")} />}
          </PermissionControl>
        </TabsContent>

        <TabsContent value="evaluation">
          <PermissionControl permissions={["players.edit", "players.view_own"]}>
            {id && <PlayerEvaluation playerId={id} clubId={clubId} isOwnProfile={isOwnProfile} />}
          </PermissionControl>
        </TabsContent>
      </Tabs>

      {/* Modal para criar conta de jogador */}
      {id && player && (
        <PlayerAccountForm
          open={isAccountFormOpen}
          onOpenChange={setIsAccountFormOpen}
          playerId={id}
          playerName={player.name}
          playerEmail={player.email}
          onSuccess={() => {
            // Recarregar perfil do jogador
            fetchPlayerProfile(clubId, id);
            toast({
              title: "Sucesso",
              description: "Conta criada com sucesso",
            });
          }}
        />
      )}

      {/* Modal para jogador editar seu próprio perfil */}
      {id && player && (
        <PlayerSelfEditDialog
          open={isEditProfileOpen}
          onOpenChange={setIsEditProfileOpen}
          player={player}
          onSuccess={() => {
            // Recarregar perfil do jogador
            fetchPlayerProfile(clubId, id);
            toast({
              title: "Sucesso",
              description: "Perfil atualizado com sucesso",
            });
          }}
        />
      )}
    </div>
  );
}
