import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarClock } from "lucide-react";
import type { AgendaEvent } from "@/api/api";

interface AgendaDayViewProps {
  events: (Omit<AgendaEvent, "date" | "endTime"> & { date: Date; endTime: Date })[];
}

const getEventTypeStyles = (type: string) => {
  const styles: Record<string, { color: string, bg: string, textColor: string }> = {
    treino: { color: "border-green-400", bg: "bg-green-50", textColor: "text-green-700" },
    jogo: { color: "border-primary", bg: "bg-primary/10", textColor: "text-primary" },
    reuniao: { color: "border-purple-400", bg: "bg-purple-50", textColor: "text-purple-700" },
    medico: { color: "border-red-400", bg: "bg-red-50", textColor: "text-red-700" },
    viagem: { color: "border-amber-400", bg: "bg-amber-50", textColor: "text-amber-700" },
    outro: { color: "border-gray-400", bg: "bg-gray-50", textColor: "text-gray-700" },
  };

  return styles[type] || styles.outro;
};

// Horários de 7h às 22h
const HOURS = Array.from({ length: 16 }, (_, i) => i + 7);

export function AgendaDayView({ events }: AgendaDayViewProps) {
  // Ordenar eventos por horário
  const sortedEvents = [...events].sort((a, b) => a.date.getTime() - b.date.getTime());

  // Criar um mapa de eventos por hora
  const eventsByHour: Record<number, AgendaDayViewProps["events"]> = {};
  sortedEvents.forEach(event => {
    const hour = event.date.getHours();
    if (!eventsByHour[hour]) {
      eventsByHour[hour] = [];
    }
    eventsByHour[hour].push(event);
  });

  return (
    <div className="relative min-h-[600px] border rounded-md p-1">
      {/* Linhas de hora */}
      {HOURS.map((hour) => (
        <div key={hour} className="flex items-start h-16 border-t relative">
          <div className="w-14 text-xs text-muted-foreground pr-2 pt-1 text-right">
            {hour}:00
          </div>
          <div className="flex-1 relative">
            {/* Eventos desta hora */}
            {eventsByHour[hour]?.map((event) => {
              const { color, bg, textColor } = getEventTypeStyles(event.type);
              const durationHours = (event.endTime.getTime() - event.date.getTime()) / (1000 * 60 * 60);
              const height = Math.max(1, Math.round(durationHours * 64)); // 64px por hora (16px altura da linha)

              return (
                <div
                  key={event.id}
                  className={`absolute left-0 right-2 rounded-md px-3 py-1 border-l-4 ${color} ${bg} overflow-hidden`}
                  style={{
                    top: `${(event.date.getMinutes() / 60) * 64}px`,
                    height: `${height}px`,
                  }}
                >
                  <div className="flex items-center gap-1.5">
                    <CalendarClock className={`h-3 w-3 ${textColor}`} />
                    <p className={`text-sm font-medium truncate ${textColor}`}>{event.title}</p>
                  </div>
                  <div className="text-xs truncate mt-0.5">{event.location}</div>
                </div>
              );
            })}
          </div>
        </div>
      ))}

      {/* Se não houver eventos */}
      {events.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <p className="text-muted-foreground">Não há eventos para esta data</p>
        </div>
      )}
    </div>
  );
}
