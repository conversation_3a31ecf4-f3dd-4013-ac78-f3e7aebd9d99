import { cn } from "@/lib/utils";

interface RequiredIndicatorProps {
  className?: string;
}

export function RequiredIndicator({ className }: RequiredIndicatorProps) {
  return (
    <span className={cn("text-destructive ml-1", className)}>
      *
    </span>
  );
}

interface LabelWithRequiredProps {
  children: React.ReactNode;
  required?: boolean;
  className?: string;
  htmlFor?: string;
}

export function LabelWithRequired({ 
  children, 
  required = false, 
  className,
  htmlFor 
}: LabelWithRequiredProps) {
  return (
    <label 
      htmlFor={htmlFor}
      className={cn("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70", className)}
    >
      {children}
      {required && <RequiredIndicator />}
    </label>
  );
}
