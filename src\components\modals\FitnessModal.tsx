import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Activity, Users, Heart, TrendingUp } from "lucide-react";
import { useDashboardStats } from "@/hooks/useDashboardStats";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { useCategoriesStore } from "@/store/useCategoriesStore";

interface FitnessModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedCategoryId?: number;
}

export function FitnessModal({ open, onOpenChange, selectedCategoryId }: FitnessModalProps) {
  const clubId = useCurrentClubId();
  const { stats, loading } = useDashboardStats(clubId, selectedCategoryId);
  const { players } = usePlayersStore();
  const { categories } = useCategoriesStore();

  // Get selected category name
  const selectedCategory = selectedCategoryId
    ? categories.find(cat => cat.id === selectedCategoryId)
    : null;

  // Filter players based on selected category if applicable
  const filteredPlayers = selectedCategoryId
    ? players.filter(player =>
        // For now, we'll use all players since category filtering would require
        // additional logic to check player_categories table
        true
      )
    : players;

  // Note: Fitness distribution removed as the system doesn't currently store fitness data
  // The stats.minutes field is not being used for fitness tracking

  // Players by status
  const playersByStatus = {
    available: filteredPlayers.filter(p => p.status === "disponivel").length,
    injured: filteredPlayers.filter(p => p.status === "lesionado").length,
    recovering: filteredPlayers.filter(p => p.status === "em recuperacao").length,
    suspended: filteredPlayers.filter(p => p.status === "suspenso").length,
    inactive: filteredPlayers.filter(p => p.status === "inativo").length,
  };

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Aptidão Física - Carregando...</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center h-64">
            <div className="text-muted-foreground">Carregando dados...</div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Status dos Atletas
            {selectedCategory && (
              <Badge variant="outline">
                {selectedCategory.name}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Total de Atletas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalActivePlayers ?? filteredPlayers.length}</div>
                <p className="text-xs text-muted-foreground">Atletas ativos</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Em Reabilitação
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {stats?.playersInRehabilitation ?? 0}
                </div>
                <p className="text-xs text-muted-foreground">Atletas em recuperação</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Aptidão Média
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  Sem dados
                </div>
                <p className="text-xs text-muted-foreground">Sistema não possui dados de aptidão física</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Disponíveis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {playersByStatus.available}
                </div>
                <p className="text-xs text-muted-foreground">Prontos para jogar</p>
              </CardContent>
            </Card>
          </div>

          {/* Note: Fitness Distribution section removed as the system doesn't store fitness data */}

          {/* Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Status dos Atletas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 mb-2">
                    Disponível
                  </Badge>
                  <div className="text-xl font-bold">{playersByStatus.available}</div>
                </div>
                <div className="text-center">
                  <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 mb-2">
                    Lesionado
                  </Badge>
                  <div className="text-xl font-bold">{playersByStatus.injured}</div>
                </div>
                <div className="text-center">
                  <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 mb-2">
                    Em Recuperação
                  </Badge>
                  <div className="text-xl font-bold">{playersByStatus.recovering}</div>
                </div>
                <div className="text-center">
                  <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 mb-2">
                    Suspenso
                  </Badge>
                  <div className="text-xl font-bold">{playersByStatus.suspended}</div>
                </div>
                <div className="text-center">
                  <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 mb-2">
                    Inativo
                  </Badge>
                  <div className="text-xl font-bold">{playersByStatus.inactive}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Categories Breakdown */}
          {stats?.playersByCategory && stats.playersByCategory.length > 1 && (
            <Card>
              <CardHeader>
                <CardTitle>Atletas por Categoria</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {stats.playersByCategory.map((category) => (
                    <div key={category.categoryId} className="flex items-center justify-between">
                      <span className="font-medium">{category.categoryName}</span>
                      <Badge variant="outline">{category.playerCount} atletas</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
