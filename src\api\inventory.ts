import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import type { Database } from "@/integrations/supabase/types";
import { INVENTORY_PERMISSIONS } from "@/constants/permissions";

// Tipos
export type InventoryProduct = {
  id: number;
  club_id: number;
  name: string;
  quantity: number;
  minimum_quantity: number;
  unit_of_measure: string;
  registration_date: string;
  expiration_date?: string | null;
  location: string | null;
  department: string;
  description: string | null;
  image_url: string | null;
  created_at: string;
  updated_at: string;
};

export type InventoryTransaction = {
  id: number;
  club_id: number;
  product_id: number;
  transaction_type: 'entrada' | 'saida';
  quantity: number;
  previous_quantity: number;
  new_quantity: number;
  user_id: string;
  notes: string | null;
  created_at: string;
  product_name?: string; // Campo adicional para junção
};

export type InventoryNotificationSettings = {
  id: number;
  club_id: number;
  threshold: number;
  notify_users: string[];
  email_notifications: boolean;
  system_notifications: boolean;
  created_at: string;
  updated_at: string;
};

// Departamentos disponíveis para produtos
export const INVENTORY_DEPARTMENTS = [
  'Material Esportivo',
  'Alimentação',
  'Mobília',
  'Manutenção',
  'Produto de Limpeza',
  'Papelaria',
  'Farmácia',
  'Suplemento',
  'Correspondencia'
];

// Funções para gerenciar produtos do estoque
export async function getInventoryProducts(
  clubId: number,
  department?: string
): Promise<InventoryProduct[]> {
  let query = supabase
    .from("inventory_products")
    .select("*")
    .eq("club_id", clubId)
    .order("name");

  if (department) {
    query = query.eq("department", department);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar produtos do estoque:", error);
    throw new Error(`Erro ao buscar produtos do estoque: ${error.message}`);
  }

  return data || [];
}

export async function getInventoryProductById(
  clubId: number,
  productId: number
): Promise<InventoryProduct> {
  const { data, error } = await supabase
    .from("inventory_products")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", productId)
    .single();

  if (error) {
    console.error("Erro ao buscar produto do estoque:", error);
    throw new Error(`Erro ao buscar produto do estoque: ${error.message}`);
  }

  return data;
}

export async function createInventoryProduct(
  clubId: number,
  productData: Omit<InventoryProduct, 'id' | 'club_id' | 'created_at' | 'updated_at'>,
  userId?: string
): Promise<InventoryProduct> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    return createProductWithoutPermissionCheck(clubId, productData);
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.CREATE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.create",
        { product_name: productData.name },
        async () => {
          return createProductWithoutPermissionCheck(clubId, productData);
        }
      );
    }
  );
}

// Função auxiliar para criar produto sem verificação de permissão
async function createProductWithoutPermissionCheck(
  clubId: number,
  productData: Omit<InventoryProduct, 'id' | 'club_id' | 'created_at' | 'updated_at'>
): Promise<InventoryProduct> {
  const { data, error } = await supabase
    .from("inventory_products")
    .insert({
      club_id: clubId,
      name: productData.name,
      quantity: productData.quantity,
      minimum_quantity: productData.minimum_quantity || 0,
      unit_of_measure: productData.unit_of_measure || 'unidade',
      registration_date: productData.registration_date,
      expiration_date: productData.expiration_date || null,
      location: productData.location,
      department: productData.department,
      description: productData.description
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar produto no estoque:", error);
    throw new Error(`Erro ao criar produto no estoque: ${error.message}`);
  }

  return data;
}

export async function updateInventoryProduct(
  clubId: number,
  productId: number,
  productData: Partial<Omit<InventoryProduct, 'id' | 'club_id' | 'created_at' | 'updated_at'>>,
  userId?: string
): Promise<InventoryProduct> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    return updateProductWithoutPermissionCheck(clubId, productId, productData);
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.update",
        { product_id: productId, ...productData },
        async () => {
          return updateProductWithoutPermissionCheck(clubId, productId, productData);
        }
      );
    }
  );
}

// Função auxiliar para atualizar produto sem verificação de permissão
async function updateProductWithoutPermissionCheck(
  clubId: number,
  productId: number,
  productData: Partial<Omit<InventoryProduct, 'id' | 'club_id' | 'created_at' | 'updated_at'>>
): Promise<InventoryProduct> {
  const { data, error } = await supabase
    .from("inventory_products")
    .update({
      name: productData.name,
      quantity: productData.quantity,
      minimum_quantity: productData.minimum_quantity,
      unit_of_measure: productData.unit_of_measure,
      registration_date: productData.registration_date,
      expiration_date: productData.expiration_date,
      location: productData.location,
      department: productData.department,
      description: productData.description,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", productId)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar produto no estoque:", error);
    throw new Error(`Erro ao atualizar produto no estoque: ${error.message}`);
  }

  return data;
}

export async function deleteInventoryProduct(
  clubId: number,
  productId: number,
  userId?: string
): Promise<boolean> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    return deleteProductWithoutPermissionCheck(clubId, productId);
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.DELETE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.delete",
        { product_id: productId },
        async () => {
          return deleteProductWithoutPermissionCheck(clubId, productId);
        }
      );
    }
  );
}

// Função auxiliar para excluir produto sem verificação de permissão
async function deleteProductWithoutPermissionCheck(
  clubId: number,
  productId: number
): Promise<boolean> {
  // Iniciar uma transação para garantir a integridade dos dados
  try {
    // 1. Excluir itens de solicitação relacionados ao produto
    const { error: deleteRequestItemsError } = await supabase
      .from("inventory_request_items")
      .delete()
      .eq("club_id", clubId)
      .eq("product_id", productId);

    if (deleteRequestItemsError) {
      console.error("Erro ao excluir itens de solicitação do produto:", deleteRequestItemsError);
      throw new Error(`Erro ao excluir itens de solicitação do produto: ${deleteRequestItemsError.message}`);
    }

    // 2. Excluir transações relacionadas ao produto
    const { error: deleteTransactionsError } = await supabase
      .from("inventory_transactions")
      .delete()
      .eq("club_id", clubId)
      .eq("product_id", productId);

    if (deleteTransactionsError) {
      console.error("Erro ao excluir transações do produto:", deleteTransactionsError);
      throw new Error(`Erro ao excluir transações do produto: ${deleteTransactionsError.message}`);
    }

    // 3. Finalmente, excluir o produto
    const { error: deleteProductError } = await supabase
      .from("inventory_products")
      .delete()
      .eq("club_id", clubId)
      .eq("id", productId);

    if (deleteProductError) {
      console.error("Erro ao excluir produto do estoque:", deleteProductError);
      throw new Error(`Erro ao excluir produto do estoque: ${deleteProductError.message}`);
    }

    return true;
  } catch (error) {
    console.error("Erro durante a exclusão do produto:", error);
    throw error; // Rejoga o erro para ser tratado pelo chamador
  }
}

// Funções para gerenciar transações do estoque
export async function getInventoryTransactions(
  clubId: number,
  productId?: number,
  startDate?: string,
  endDate?: string
): Promise<InventoryTransaction[]> {
  let query = supabase
    .from("inventory_transactions")
    .select(`
      *,
      inventory_products (name)
    `)
    .eq("club_id", clubId)
    .order("created_at", { ascending: false });

  if (productId) {
    query = query.eq("product_id", productId);
  }

  if (startDate) {
    query = query.gte("created_at", startDate);
  }

  if (endDate) {
    query = query.lte("created_at", endDate);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar transações do estoque:", error);
    throw new Error(`Erro ao buscar transações do estoque: ${error.message}`);
  }

  // Formatar os dados para incluir o nome do produto
  return (data || []).map(item => ({
    ...item,
    product_name: item.inventory_products?.name
  }));
}

export async function registerInventoryTransaction(
  clubId: number,
  productId: number,
  quantity: number,
  transactionType: 'entrada' | 'saida',
  notes?: string,
  userId?: string
): Promise<InventoryTransaction> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    return registerTransactionWithoutPermissionCheck(clubId, productId, quantity, transactionType, notes);
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.transaction",
        {
          product_id: productId,
          quantity,
          transaction_type: transactionType
        },
        async () => {
          return registerTransactionWithoutPermissionCheck(clubId, productId, quantity, transactionType, notes);
        }
      );
    }
  );
}

// Função auxiliar para registrar transação sem verificação de permissão
async function registerTransactionWithoutPermissionCheck(
  clubId: number,
  productId: number,
  quantity: number,
  transactionType: 'entrada' | 'saida',
  notes?: string
): Promise<InventoryTransaction> {
  // Obter o ID do usuário e garantir que seja um UUID válido
  const userId = (await supabase.auth.getUser()).data.user?.id;
  if (!userId) {
    throw new Error("Usuário não autenticado");
  }

  // Usar a função SQL para atualizar a quantidade e registrar a transação
  const { error } = await supabase.rpc(
    'update_inventory_product_quantity',
    {
      p_club_id: clubId,
      p_product_id: productId,
      p_quantity: quantity,
      p_transaction_type: transactionType,
      p_user_id: userId,
      p_notes: notes || null
    }
  );

  if (error) {
    console.error("Erro ao registrar transação no estoque:", error);
    throw new Error(`Erro ao registrar transação no estoque: ${error.message}`);
  }

  // Buscar a transação recém-criada
  const { data: transactionData, error: transactionError } = await supabase
    .from("inventory_transactions")
    .select(`
      *,
      inventory_products (name)
    `)
    .eq("club_id", clubId)
    .eq("product_id", productId)
    .order("created_at", { ascending: false })
    .limit(1)
    .single();

  if (transactionError) {
    console.error("Erro ao buscar transação criada:", transactionError);
    throw new Error(`Erro ao buscar transação criada: ${transactionError.message}`);
  }

  return {
    ...transactionData,
    product_name: transactionData.inventory_products?.name
  } as InventoryTransaction;
}

// Funções para gerenciar produtos com baixo estoque
export async function getLowStockProducts(
  clubId: number
): Promise<InventoryProduct[]> {
  // Primeiro, vamos buscar todos os produtos
  const { data, error } = await supabase
    .from("inventory_products")
    .select("*")
    .eq("club_id", clubId)
    .order("quantity");

  if (error) {
    console.error("Erro ao buscar produtos com estoque baixo:", error);
    throw new Error(`Erro ao buscar produtos com estoque baixo: ${error.message}`);
  }

  // Depois, filtramos manualmente os produtos com estoque baixo
  const lowStockProducts = (data || []).filter(product =>
    product.quantity <= (product.minimum_quantity || 0)
  );

  return lowStockProducts;
}

// Funções para gerenciar configurações de notificação
export async function getInventoryNotificationSettings(
  clubId: number
): Promise<InventoryNotificationSettings> {
  const { data, error } = await supabase
    .from("inventory_notifications")
    .select("*")
    .eq("club_id", clubId)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 = No rows returned
    console.error("Erro ao buscar configurações de notificação:", error);
    throw new Error(`Erro ao buscar configurações de notificação: ${error.message}`);
  }

  // Se não houver configurações, retornar valores padrão
  if (!data) {
    return {
      id: 0,
      club_id: clubId,
      threshold: 5,
      notify_users: [],
      email_notifications: true,
      system_notifications: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  return {
    ...data,
    notify_users: data.notify_users || []
  };
}

export async function updateInventoryNotificationSettings(
  clubId: number,
  settings: Partial<Omit<InventoryNotificationSettings, 'id' | 'club_id' | 'created_at' | 'updated_at'>>,
  userId?: string
): Promise<InventoryNotificationSettings> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    return updateNotificationSettingsWithoutPermissionCheck(clubId, settings);
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.notification_settings.update",
        { settings },
        async () => {
          return updateNotificationSettingsWithoutPermissionCheck(clubId, settings);
        }
      );
    }
  );
}

// Função auxiliar para atualizar configurações de notificação sem verificação de permissão
async function updateNotificationSettingsWithoutPermissionCheck(
  clubId: number,
  settings: Partial<Omit<InventoryNotificationSettings, 'id' | 'club_id' | 'created_at' | 'updated_at'>>
): Promise<InventoryNotificationSettings> {
  // Verificar se já existe uma configuração
  const { data: existingData } = await supabase
    .from("inventory_notifications")
    .select("id")
    .eq("club_id", clubId)
    .single();

  let result: any;

  if (existingData) {
    // Atualizar configuração existente
    const { data, error } = await supabase
      .from("inventory_notifications")
      .update({
        threshold: settings.threshold,
        notify_users: settings.notify_users,
        email_notifications: settings.email_notifications,
        system_notifications: settings.system_notifications,
        updated_at: new Date().toISOString()
      })
      .eq("club_id", clubId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar configurações de notificação:", error);
      throw new Error(`Erro ao atualizar configurações de notificação: ${error.message}`);
    }

    result = data;
  } else {
    // Criar nova configuração
    const { data, error } = await supabase
      .from("inventory_notifications")
      .insert({
        club_id: clubId,
        threshold: settings.threshold || 5,
        notify_users: settings.notify_users || [],
        email_notifications: settings.email_notifications !== undefined ? settings.email_notifications : true,
        system_notifications: settings.system_notifications !== undefined ? settings.system_notifications : true
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar configurações de notificação:", error);
      throw new Error(`Erro ao criar configurações de notificação: ${error.message}`);
    }

    result = data;
  }

  return {
    id: result.id,
    club_id: result.club_id,
    threshold: result.threshold,
    notify_users: result.notify_users || [],
    email_notifications: result.email_notifications,
    system_notifications: result.system_notifications,
    created_at: result.created_at,
    updated_at: result.updated_at
  };
}
