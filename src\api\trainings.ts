// Funções relacionadas a Trainings serão migradas para cá a partir do api.ts

import { supabase } from "@/integrations/supabase/client";
import { getCategoryPlayers } from "./categories";

// Exporta tipos necessários para re-export em api.ts
export type Training = {
  id: number;
  club_id: number;
  name: string;
  type: string;
  date: string;
  time?: string;
  start_time?: string;
  end_time?: string;
  location: string;
  status: "concluído" | "em andamento" | "agendado";
  progress: number;
  coach: string;
  participants: number;
  description: string;
  category_id?: number;
  category_name?: string;
  images?: TrainingImage[];
};

export type TrainingGoal = {
  id: number;
  club_id: number;
  name: string;
  description: string;
  type: string;
  target_value: number;
  current_value: number;
};

export type TrainingFinalization = {
  id: number;
  training_id: number;
  summary: string;
  description: string;
};

export type TrainingExercise = {
  id: number;
  training_id: number;
  exercise_id: number;
  order_in_training: number;
  notes?: string;
  exercises?: Exercise;
};

export type Exercise = {
  id: number;
  club_id: number;
  name: string;
  description?: string;
  category?: string;
  difficulty?: string;
  created_at?: string;
};

export type PhysicalProgress = {
  id: number;
  club_id: number;
  date: string;
  weight: number;
  height: number;
  body_fat: number;
};

export type TrainingImage = {
  id: number;
  club_id: number;
  training_id: number;
  image_url: string;
  image_order: number;
  created_at?: string;
};

// Funções para Trainings
export async function getTrainings(clubId: number): Promise<Training[]> {
  // Buscar treinos com informações de categoria
  const { data, error } = await supabase
    .from("trainings")
    .select(`
      *,
      categories:category_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId);

  if (error) {
    console.error("Erro ao buscar treinos:", error);
    throw new Error(`Erro ao buscar treinos: ${error.message}`);
  }

  // Buscar as associações de jogadores para todos os treinos
  const { data: trainingPlayersData, error: trainingPlayersError } = await supabase
    .from("training_players")
    .select("training_id, player_id")
    .eq("club_id", clubId);

  if (trainingPlayersError) {
    console.error("Erro ao buscar associações de jogadores:", trainingPlayersError);
    // Não lançamos erro aqui para não impedir a exibição dos treinos
  }

  // Criar um mapa de treino_id -> lista de player_ids
  const trainingPlayersMap: Record<number, string[]> = {};
  if (trainingPlayersData) {
    trainingPlayersData.forEach(tp => {
      if (!trainingPlayersMap[tp.training_id]) {
        trainingPlayersMap[tp.training_id] = [];
      }
      trainingPlayersMap[tp.training_id].push(tp.player_id);
    });
  }

  // Converter os dados do banco para o formato esperado pelo app
  const trainings = data.map(item => {
    const notesParts = item.notes?.split('|') || Array(9).fill('');
    const timeStr = notesParts[2] || '10:00';
    const timeComponents = timeStr.split('-');
    const startTime = timeComponents[0].trim();
    const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

    return {
      id: item.id,
      club_id: item.club_id,
      name: notesParts[0] || 'Treino',
      type: notesParts[1] || 'geral',
      date: item.date,
      time: timeStr,
      start_time: startTime,
      end_time: endTime,
      location: notesParts[3] || 'Campo Principal',
      status: notesParts[4] || 'agendado',
      progress: parseInt(notesParts[5] || '0'),
      coach: notesParts[6] || 'Técnico',
      participants: parseInt(notesParts[7] || '0'),
      description: notesParts[8] || '',
      category_id: item.category_id,
      category_name: item.categories?.name,
      player_ids: trainingPlayersMap[item.id] || [] // Adicionar os IDs dos jogadores associados
    } as Training;
  });

  // Não filtrar treinos concluídos automaticamente - deixar que a interface controle a exibição
  // Buscar imagens para cada treino
  const trainingsWithImages = await Promise.all(
    trainings.map(async (training) => {
      try {
        const images = await getTrainingImages(training.id);
        return {
          ...training,
          images
        };
      } catch (error) {
        console.error(`Erro ao buscar imagens do treino ${training.id}:`, error);
        return {
          ...training,
          images: []
        };
      }
    })
  );

  // Ordenar treinos: primeiro os agendados por data (mais próximos primeiro),
  // depois os em andamento
  const today = new Date();

  return trainingsWithImages.sort((a, b) => {
    // Primeiro, separar por status
    if (a.status !== b.status) {
      if (a.status === "agendado") return -1;
      if (b.status === "agendado") return 1;
      if (a.status === "em andamento") return -1;
      if (b.status === "em andamento") return 1;
    }

    // Para treinos agendados, ordenar por data (mais próximos primeiro)
    if (a.status === "agendado") {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);

      // Se ambas as datas são futuras ou ambas são passadas
      if ((dateA >= today && dateB >= today) || (dateA < today && dateB < today)) {
        return dateA.getTime() - dateB.getTime();
      }

      // Datas futuras vêm antes de datas passadas
      return dateA >= today ? -1 : 1;
    }

    // Para outros status, ordenar por data (mais recentes primeiro)
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateB.getTime() - dateA.getTime();
  });
}

// Nova função para buscar treinos concluídos com filtros de ano e mês
export async function getCompletedTrainings(clubId: number, year?: number, month?: number): Promise<Training[]> {
  // Buscar treinos com informações de categoria
  const { data, error } = await supabase
    .from("trainings")
    .select(`
      *,
      categories:category_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId);

  if (error) {
    console.error("Erro ao buscar treinos concluídos:", error);
    throw new Error(`Erro ao buscar treinos concluídos: ${error.message}`);
  }

  // Buscar as associações de jogadores para todos os treinos
  const { data: trainingPlayersData, error: trainingPlayersError } = await supabase
    .from("training_players")
    .select("training_id, player_id")
    .eq("club_id", clubId);

  if (trainingPlayersError) {
    console.error("Erro ao buscar associações de jogadores:", trainingPlayersError);
    // Não lançamos erro aqui para não impedir a exibição dos treinos
  }

  // Criar um mapa de treino_id -> lista de player_ids
  const trainingPlayersMap: Record<number, string[]> = {};
  if (trainingPlayersData) {
    trainingPlayersData.forEach(tp => {
      if (!trainingPlayersMap[tp.training_id]) {
        trainingPlayersMap[tp.training_id] = [];
      }
      trainingPlayersMap[tp.training_id].push(tp.player_id);
    });
  }

  // Converter os dados do banco para o formato esperado pelo app
  const trainings = data.map(item => {
    const notesParts = item.notes?.split('|') || Array(9).fill('');
    const timeStr = notesParts[2] || '10:00';
    const timeComponents = timeStr.split('-');
    const startTime = timeComponents[0].trim();
    const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

    return {
      id: item.id,
      club_id: item.club_id,
      name: notesParts[0] || 'Treino',
      type: notesParts[1] || 'geral',
      date: item.date,
      time: timeStr,
      start_time: startTime,
      end_time: endTime,
      location: notesParts[3] || 'Campo Principal',
      status: notesParts[4] || 'agendado',
      progress: parseInt(notesParts[5] || '0'),
      coach: notesParts[6] || 'Técnico',
      participants: parseInt(notesParts[7] || '0'),
      description: notesParts[8] || '',
      category_id: item.category_id,
      category_name: item.categories?.name,
      player_ids: trainingPlayersMap[item.id] || []
    } as Training;
  });

  // Filtrar apenas treinos concluídos
  let completedTrainings = trainings.filter(training => training.status === "concluído");

  // Aplicar filtros de ano e mês se fornecidos
  if (year || month) {
    completedTrainings = completedTrainings.filter(training => {
      const trainingDate = new Date(training.date);

      if (year && trainingDate.getFullYear() !== year) {
        return false;
      }

      if (month && trainingDate.getMonth() + 1 !== month) {
        return false;
      }

      return true;
    });
  }

  // Buscar imagens para cada treino concluído
  const completedTrainingsWithImages = await Promise.all(
    completedTrainings.map(async (training) => {
      try {
        const images = await getTrainingImages(training.id);
        return {
          ...training,
          images
        };
      } catch (error) {
        console.error(`Erro ao buscar imagens do treino ${training.id}:`, error);
        return {
          ...training,
          images: []
        };
      }
    })
  );

  // Ordenar por data (mais recentes primeiro)
  return completedTrainingsWithImages.sort((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateB.getTime() - dateA.getTime();
  });
}


export async function createTraining(clubId: number, training: Omit<Training, "id">): Promise<Training> {
  const notes = `${training.name}|${training.type}|${training.time}|${training.location}|${training.status}|${training.progress}|${training.coach}|${training.participants}|${training.description}`;

  // Inserir o treino com a categoria
  const { data, error } = await supabase
    .from("trainings")
    .insert({
      club_id: clubId,
      date: training.date,
      notes: notes,
      category_id: training.category_id
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar treino:", error);
    throw new Error(`Erro ao criar treino: ${error.message}`);
  }

  // Variáveis para armazenar dados da categoria
  let categoryName = "";
  let categoryPlayers: any[] = [];
  let playerIdsInCategory: string[] = [];

  // Buscar informações da categoria e jogadores em uma única operação
  if (training.category_id) {
    try {
      // 1. Buscar nome da categoria
      const { data: categoryData } = await supabase
        .from("categories")
        .select("name")
        .eq("id", training.category_id)
        .single();

      if (categoryData) {
        categoryName = categoryData.name;
      }

      // 2. Buscar IDs dos jogadores na categoria
      const { data: playerCategoriesData } = await supabase
        .from("player_categories")
        .select("player_id")
        .eq("club_id", clubId)
        .eq("category_id", training.category_id);

      if (playerCategoriesData && playerCategoriesData.length > 0) {
        playerIdsInCategory = playerCategoriesData.map(item => item.player_id);

        // 3. Buscar detalhes dos jogadores
        const { data: playersData } = await supabase
          .from("players")
          .select("*")
          .in("id", playerIdsInCategory);

        if (playersData) {
          categoryPlayers = playersData;
        }
      }

      // 4. Associar jogadores ao treino
      if (categoryPlayers.length > 0) {
        const trainingPlayersData = categoryPlayers.map(player => ({
          club_id: clubId,
          training_id: data.id,
          player_id: player.id
        }));

        // Inserir em lote
        const { error: batchError } = await supabase
          .from("training_players")
          .insert(trainingPlayersData);

        if (batchError) {
          console.error("Erro ao associar jogadores ao treino:", batchError);
        }
      }

      // 5. Criar notificações para os jogadores com contas
      const { data: playersWithAccounts } = await supabase
        .from("players")
        .select("id, name, user_id")
        .eq("club_id", clubId)
        .not("user_id", "is", null)
        .in("id", playerIdsInCategory);

      if (playersWithAccounts && playersWithAccounts.length > 0) {
        // Criar notificações para cada jogador
        for (const player of playersWithAccounts) {
          if (player.user_id) {
            await supabase.from("notifications").insert({
              club_id: clubId,
              user_id: player.user_id,
              title: "Novo treino agendado",
              message: `Um novo treino de ${training.type} foi agendado para ${training.date} às ${training.time.split('-')[0]}.`,
              type: "training",
              reference_id: data.id.toString(),
              reference_type: "training"
            });
          }
        }
      }
    } catch (error) {
      console.error("Erro ao processar categoria e jogadores:", error);
      // Não lançamos erro aqui para não impedir a criação do treino
    }
  }

  return {
    id: data.id,
    club_id: data.club_id,
    name: training.name,
    type: training.type,
    date: data.date,
    time: training.time,
    location: training.location,
    status: training.status,
    progress: training.progress,
    coach: training.coach,
    participants: training.participants,
    description: training.description,
    category_id: training.category_id,
    category_name: categoryName
  } as Training;
}

export async function updateTraining(clubId: number, id: number, training: Partial<Training>): Promise<Training> {
  // Primeiro precisamos obter o treino atual
  const { data: existingTraining, error: fetchError } = await supabase
    .from("trainings")
    .select("*, categories:category_id(id, name)")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (fetchError) {
    console.error("Erro ao buscar treino para atualização:", fetchError);
    throw new Error(`Erro ao buscar treino para atualização: ${fetchError.message}`);
  }

  // Parseamos o notes existente
  const existingNotesParts = existingTraining.notes?.split('|') || Array(9).fill('');

  // Atualizamos apenas os campos fornecidos
  const updatedNotesParts = [...existingNotesParts];
  if (training.name) updatedNotesParts[0] = training.name;
  if (training.type) updatedNotesParts[1] = training.type;

  // Handle time field updates
  if (training.time) {
    // If time is directly provided, use it
    updatedNotesParts[2] = training.time;
  } else if (training.start_time && training.end_time) {
    // If start_time and end_time are provided, combine them
    updatedNotesParts[2] = `${training.start_time}-${training.end_time}`;
  }

  if (training.location) updatedNotesParts[3] = training.location;
  if (training.status) updatedNotesParts[4] = training.status;
  if (training.progress !== undefined) updatedNotesParts[5] = String(training.progress);
  if (training.coach) updatedNotesParts[6] = training.coach;
  if (training.participants !== undefined) updatedNotesParts[7] = String(training.participants);
  if (training.description) updatedNotesParts[8] = training.description;

  const updatedNotes = updatedNotesParts.join('|');

  // Preparar dados para atualização
  const updateData: any = {
    date: training.date || existingTraining.date,
    notes: updatedNotes
  };

  // Se a categoria foi alterada, atualizar o campo category_id
  if (training.category_id !== undefined) {
    updateData.category_id = training.category_id;
  }

  // Atualizar o treino
  const { data, error } = await supabase
    .from("trainings")
    .update(updateData)
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar treino:", error);
    throw new Error(`Erro ao atualizar treino: ${error.message}`);
  }

  // Se a categoria foi alterada, atualizar os jogadores associados
  let categoryName = existingTraining.categories?.name || "";

  if (training.category_id !== undefined && training.category_id !== existingTraining.category_id) {
    try {
      // Remover associações existentes
      await supabase
        .from("training_players")
        .delete()
        .eq("training_id", id)
        .eq("club_id", clubId);

      // Se a nova categoria não for nula, associar os jogadores da nova categoria
      if (training.category_id) {
        // 1. Buscar nome da categoria
        const { data: categoryData } = await supabase
          .from("categories")
          .select("name")
          .eq("id", training.category_id)
          .single();

        if (categoryData) {
          categoryName = categoryData.name;
        }

        // 2. Buscar IDs dos jogadores na categoria
        const { data: playerCategoriesData } = await supabase
          .from("player_categories")
          .select("player_id")
          .eq("club_id", clubId)
          .eq("category_id", training.category_id);

        if (playerCategoriesData && playerCategoriesData.length > 0) {
          const playerIdsInCategory = playerCategoriesData.map(item => item.player_id);

          // 3. Buscar detalhes dos jogadores
          const { data: playersData } = await supabase
            .from("players")
            .select("*")
            .in("id", playerIdsInCategory);

          if (playersData && playersData.length > 0) {
            // 4. Associar jogadores ao treino
            const trainingPlayersData = playersData.map(player => ({
              club_id: clubId,
              training_id: id,
              player_id: player.id
            }));

            // Inserir em lote
            await supabase
              .from("training_players")
              .insert(trainingPlayersData);
          }
        }
      } else {
        categoryName = "";
      }
    } catch (error) {
      console.error("Erro ao atualizar jogadores do treino:", error);
      // Não lançamos erro aqui para não impedir a atualização do treino
    }
  }

  return {
    id: data.id,
    club_id: data.club_id,
    name: updatedNotesParts[0],
    type: updatedNotesParts[1],
    date: data.date,
    time: updatedNotesParts[2],
    location: updatedNotesParts[3],
    status: updatedNotesParts[4] as "concluído" | "em andamento" | "agendado",
    progress: parseInt(updatedNotesParts[5]),
    coach: updatedNotesParts[6],
    participants: parseInt(updatedNotesParts[7]),
    description: updatedNotesParts[8],
    category_id: training.category_id !== undefined ? training.category_id : existingTraining.category_id,
    category_name: categoryName
  } as Training;
}

export async function deleteTraining(clubId: number, id: number): Promise<boolean> {
  try {
    // Primeiro, excluir registros relacionados

    // 1. Excluir associações com jogadores
    await supabase
      .from("training_players")
      .delete()
      .eq("training_id", id)
      .eq("club_id", clubId);

    // 2. Excluir imagens do treino
    await supabase
      .from("training_images")
      .delete()
      .eq("training_id", id)
      .eq("club_id", clubId);

    // 3. Excluir exercícios do treino
    await supabase
      .from("training_exercises")
      .delete()
      .eq("training_id", id);

    // Por fim, excluir o treino
    const { error } = await supabase
      .from("trainings")
      .delete()
      .eq("club_id", clubId)
      .eq("id", id);

    if (error) {
      console.error("Erro ao deletar treino:", error);
      throw new Error(`Erro ao deletar treino: ${error.message}`);
    }

    return true;
  } catch (error) {
    console.error("Erro ao deletar treino e registros relacionados:", error);
    throw new Error(`Erro ao deletar treino e registros relacionados: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function finalizeTraining(clubId: number, id: number, summary: string, description: string): Promise<boolean> {
  // Primeiro precisamos obter o treino atual
  const { data: existingTraining, error: fetchError } = await supabase
    .from("trainings")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (fetchError) {
    console.error("Erro ao buscar treino para finalização:", fetchError);
    throw new Error(`Erro ao buscar treino para finalização: ${fetchError.message}`);
  }

  const existingNotesParts = existingTraining.notes?.split('|') || Array(9).fill('');
  existingNotesParts[4] = "concluído";
  existingNotesParts[5] = "100";
  existingNotesParts[8] = description;
  const updatedNotes = existingNotesParts.join('|');

  const { error } = await supabase
    .from("trainings")
    .update({ notes: updatedNotes } as any)
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Erro ao finalizar treino:", error);
    throw new Error(`Erro ao finalizar treino: ${error.message}`);
  }

  return true;
}

// Funções para Exercícios do Treino

// Funções para Exercícios
export async function getExercises(clubId: number): Promise<Exercise[]> {
  const { data, error } = await supabase
    .from("exercises")
    .select("*")
    .eq("club_id", clubId);
  if (error) throw new Error(error.message);
  return (data || []) as Exercise[];
}

export async function createExercise(clubId: number, exercise: { name: string; description?: string; category: string; difficulty: string }) {
  const { data, error } = await supabase
    .from("exercises")
    .insert({
      club_id: clubId,
      name: exercise.name,
      description: exercise.description,
      category: exercise.category,
      difficulty: exercise.difficulty,
    })
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data;
}

// Funções para Objetivos da Semana
export async function getTrainingGoals(clubId: number): Promise<TrainingGoal[]> {
  const { data, error } = await supabase
    .from("training_goals")
    .select("*")
    .eq("club_id", clubId);
  if (error) throw new Error(error.message);
  return (data || []) as TrainingGoal[];
}

export async function createTrainingGoal(clubId: number, goal: Omit<TrainingGoal, "id">): Promise<TrainingGoal> {
  const { data, error } = await supabase
    .from("training_goals")
    .insert({ ...goal, club_id: clubId })
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as TrainingGoal;
}

export async function updateTrainingGoal(clubId: number, id: number, goal: Partial<TrainingGoal>): Promise<TrainingGoal> {
  const { data, error } = await supabase
    .from("training_goals")
    .update({ ...goal })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as TrainingGoal;
}

export async function deleteTrainingGoal(clubId: number, id: number): Promise<boolean> {
  const { error } = await supabase
    .from("training_goals")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);
  if (error) throw new Error(error.message);
  return true;
}

// Funções para Progresso Físico
export async function getPhysicalProgress(clubId: number): Promise<PhysicalProgress[]> {
  const { data, error } = await supabase
    .from("physical_progress")
    .select("*")
    .eq("club_id", clubId);
  if (error) throw new Error(error.message);
  return (data || []) as PhysicalProgress[];
}

export async function createPhysicalProgress(clubId: number, progress: Omit<PhysicalProgress, "id">): Promise<PhysicalProgress> {
  const { data, error } = await supabase
    .from("physical_progress")
    .insert({ ...progress, club_id: clubId })
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as PhysicalProgress;
}

// Funções para Exercícios do Treino
export async function getTrainingExercises(trainingId: number): Promise<TrainingExercise[]> {
  const { data, error } = await supabase
    .from("training_exercises")
    .select("* , exercises(*)")
    .eq("training_id", trainingId);
  if (error) throw new Error(error.message);
  return (data || []) as TrainingExercise[];
}

export async function addExerciseToTraining(trainingId: number, exerciseId: number, order?: number, notes?: string): Promise<TrainingExercise> {
  const { data, error } = await supabase
    .from("training_exercises")
    .insert({ training_id: trainingId, exercise_id: exerciseId, order_in_training: order, notes })
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as TrainingExercise;
}

export async function removeExerciseFromTraining(trainingExerciseId: number): Promise<boolean> {
  const { error } = await supabase
    .from("training_exercises")
    .delete()
    .eq("id", trainingExerciseId);
  if (error) throw new Error(error.message);
  return true;
}

// Funções para gerenciar imagens de treino
export async function getTrainingImages(trainingId: number): Promise<TrainingImage[]> {
  const { data, error } = await supabase
    .from("training_images")
    .select("*")
    .eq("training_id", trainingId)
    .order("image_order");

  if (error) {
    console.error("Erro ao buscar imagens do treino:", error);
    throw new Error(`Erro ao buscar imagens do treino: ${error.message}`);
  }

  return data as TrainingImage[];
}

export async function addTrainingImage(
  clubId: number,
  trainingId: number,
  imageUrl: string,
  imageOrder: number
): Promise<TrainingImage> {
  const { data, error } = await supabase
    .from("training_images")
    .insert({
      club_id: clubId,
      training_id: trainingId,
      image_url: imageUrl,
      image_order: imageOrder
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao adicionar imagem ao treino:", error);
    throw new Error(`Erro ao adicionar imagem ao treino: ${error.message}`);
  }

  return data as TrainingImage;
}

export async function deleteTrainingImage(imageId: number): Promise<boolean> {
  const { error } = await supabase
    .from("training_images")
    .delete()
    .eq("id", imageId);

  if (error) {
    console.error("Erro ao excluir imagem do treino:", error);
    throw new Error(`Erro ao excluir imagem do treino: ${error.message}`);
  }

  return true;
}

// Funções para gerenciar jogadores em treinos
export async function getTrainingPlayers(trainingId: number): Promise<any[]> {
  const { data, error } = await supabase
    .from("training_players")
    .select(`
      *,
      players:player_id (
        id,
        name,
        position,
        number,
        image
      )
    `)
    .eq("training_id", trainingId);

  if (error) {
    console.error("Erro ao buscar jogadores do treino:", error);
    throw new Error(`Erro ao buscar jogadores do treino: ${error.message}`);
  }

  return data.map(item => item.players);
}

// Função para buscar treinos por categoria
export async function getTrainingsByCategory(clubId: number, categoryId: number): Promise<Training[]> {
  const { data, error } = await supabase
    .from("trainings")
    .select(`
      *,
      categories:category_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId)
    .eq("category_id", categoryId);

  if (error) {
    console.error("Erro ao buscar treinos por categoria:", error);
    throw new Error(`Erro ao buscar treinos por categoria: ${error.message}`);
  }

  // Converter os dados do banco para o formato esperado pelo app
  const trainings = data.map(item => {
    const notesParts = item.notes?.split('|') || Array(9).fill('');
    const timeStr = notesParts[2] || '10:00';
    const timeComponents = timeStr.split('-');
    const startTime = timeComponents[0].trim();
    const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

    return {
      id: item.id,
      club_id: item.club_id,
      name: notesParts[0] || 'Treino',
      type: notesParts[1] || 'geral',
      date: item.date,
      time: timeStr,
      start_time: startTime,
      end_time: endTime,
      location: notesParts[3] || 'Campo Principal',
      status: notesParts[4] || 'agendado',
      progress: parseInt(notesParts[5] || '0'),
      coach: notesParts[6] || 'Técnico',
      participants: parseInt(notesParts[7] || '0'),
      description: notesParts[8] || '',
      category_id: item.category_id,
      category_name: item.categories?.name
    } as Training;
  });

  // Ordenar treinos: primeiro os agendados por data (mais próximos primeiro)
  const today = new Date();

  return trainings.sort((a, b) => {
    // Primeiro, separar por status
    if (a.status !== b.status) {
      if (a.status === "agendado") return -1;
      if (b.status === "agendado") return 1;
      if (a.status === "em andamento") return -1;
      if (b.status === "em andamento") return 1;
    }

    // Para treinos agendados, ordenar por data (mais próximos primeiro)
    if (a.status === "agendado") {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);

      // Se ambas as datas são futuras ou ambas são passadas
      if ((dateA >= today && dateB >= today) || (dateA < today && dateB < today)) {
        return dateA.getTime() - dateB.getTime();
      }

      // Datas futuras vêm antes de datas passadas
      return dateA >= today ? -1 : 1;
    }

    // Para treinos concluídos, ordenar por data (mais recentes primeiro)
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateB.getTime() - dateA.getTime();
  });
}

// Função para buscar treinos por temporada e categoria (para o dashboard)
export async function getUpcomingTrainingsBySeasonAndCategory(clubId: number, seasonId: number, categoryId?: number): Promise<Training[]> {
  const today = new Date();
  const todayStr = today.toISOString().split('T')[0]; // Formato YYYY-MM-DD

  // Construir a query base
  let query = supabase
    .from("trainings")
    .select(`
      *,
      categories:category_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId)
    .gte("date", todayStr); // Apenas treinos futuros

  // Adicionar filtro por categoria se fornecido
  if (categoryId) {
    query = query.eq("category_id", categoryId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar treinos por temporada e categoria:", error);
    throw new Error(`Erro ao buscar treinos por temporada e categoria: ${error.message}`);
  }

  // Converter os dados do banco para o formato esperado pelo app
  const trainings = data.map(item => {
    const notesParts = item.notes?.split('|') || Array(9).fill('');
    const timeStr = notesParts[2] || '10:00';
    const timeComponents = timeStr.split('-');
    const startTime = timeComponents[0].trim();
    const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

    return {
      id: item.id,
      club_id: item.club_id,
      name: notesParts[0] || 'Treino',
      type: notesParts[1] || 'geral',
      date: item.date,
      time: timeStr,
      start_time: startTime,
      end_time: endTime,
      location: notesParts[3] || 'Campo Principal',
      status: notesParts[4] || 'agendado',
      progress: parseInt(notesParts[5] || '0'),
      coach: notesParts[6] || 'Técnico',
      participants: parseInt(notesParts[7] || '0'),
      description: notesParts[8] || '',
      category_id: item.category_id,
      category_name: item.categories?.name
    } as Training;
  });

  // Ordenar por data (mais próximos primeiro)
  return trainings.sort((a, b) => {
    const dateTimeA = new Date(`${a.date}T${a.start_time || '00:00'}`);
    const dateTimeB = new Date(`${b.date}T${b.start_time || '00:00'}`);
    return dateTimeA.getTime() - dateTimeB.getTime();
  });
}

// Função para buscar treinos futuros de um jogador
export async function getPlayerUpcomingTrainings(clubId: number, playerId: string): Promise<Training[]> {
  const today = new Date();
  const todayStr = today.toISOString().split('T')[0]; // Formato YYYY-MM-DD
  const now = today.getTime();

  // Buscar treinos onde o jogador está associado
  const { data, error } = await supabase
    .from("training_players")
    .select(`
      training_id,
      trainings:training_id (
        *,
        categories:category_id (
          id,
          name
        )
      )
    `)
    .eq("club_id", clubId)
    .eq("player_id", playerId);

  if (error) {
    console.error("Erro ao buscar treinos do jogador:", error);
    throw new Error(`Erro ao buscar treinos do jogador: ${error.message}`);
  }

  // Filtrar apenas treinos futuros e converter para o formato esperado
  const trainings = data
    .filter(item => {
      const training = item.trainings;
      if (!training) return false;

      const notesParts = training.notes?.split('|') || Array(9).fill('');
      const timeStr = notesParts[2] || '10:00';
      const timeComponents = timeStr.split('-');
      const startTime = timeComponents[0].trim();

      // Criar um objeto Date combinando a data do treino com o horário de início
      const trainingDateTime = new Date(`${training.date}T${startTime}`);

      return trainingDateTime.getTime() >= now &&
             (notesParts[4] === 'agendado' || notesParts[4] === 'em andamento');
    })
    .map(item => {
      const training = item.trainings;
      const notesParts = training.notes?.split('|') || Array(9).fill('');
      const timeStr = notesParts[2] || '10:00';
      const timeComponents = timeStr.split('-');
      const startTime = timeComponents[0].trim();
      const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

      return {
        id: training.id,
        club_id: training.club_id,
        name: notesParts[0] || 'Treino',
        type: notesParts[1] || 'geral',
        date: training.date,
        time: timeStr,
        start_time: startTime,
        end_time: endTime,
        location: notesParts[3] || 'Campo Principal',
        status: notesParts[4] || 'agendado',
        progress: parseInt(notesParts[5] || '0'),
        coach: notesParts[6] || 'Técnico',
        participants: parseInt(notesParts[7] || '0'),
        description: notesParts[8] || '',
        category_id: training.category_id,
        category_name: training.categories?.name,
        player_ids: [playerId] // Incluir o ID do jogador atual
      } as Training;
    });

  // Buscar imagens para cada treino
  const trainingsWithImages = await Promise.all(
    trainings.map(async (training) => {
      try {
        const images = await getTrainingImages(training.id);
        return {
          ...training,
          images
        };
      } catch (error) {
        console.error(`Erro ao buscar imagens do treino ${training.id}:`, error);
        return {
          ...training,
          images: []
        };
      }
    })
  );

  // Ordenar por data (mais próximos primeiro)
  return trainingsWithImages.sort((a, b) => {
    const dateTimeA = new Date(`${a.date}T${a.start_time || '00:00'}`);
    const dateTimeB = new Date(`${b.date}T${b.start_time || '00:00'}`);
    return dateTimeA.getTime() - dateTimeB.getTime();
  });
}
