import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

// Tipos
export type CollaboratorAccommodation = Database["public"]["Tables"]["collaborator_accommodations"]["Row"] & {
  collaborator_name?: string;
  accommodation_name?: string;
  collaborator_role?: string;
  collaborator_registration_number?: string;
  room_number?: string;
  // Add these properties to fix TypeScript errors
  collaborators?: {
    full_name: string;
    role?: string;
    registration_number?: string;
  };
  accommodations?: {
    name: string;
    type?: string;
  };
  hotel_rooms?: {
    room_number: string;
  };
};

// Funções para gerenciar associações entre colaboradores e alojamentos
export async function getCollaboratorAccommodations(clubId: number, collaboratorId?: number): Promise<CollaboratorAccommodation[]> {
  let query = supabase
    .from("collaborator_accommodations")
    .select(`
      *,
      collaborators:collaborator_id (full_name, role, registration_number),
      accommodations:accommodation_id (name, type),
      hotel_rooms!hotel_room_id (room_number)
    `)
    .eq("club_id", clubId as any);

  if (collaboratorId) {
    query = query.eq("collaborator_id", collaboratorId as any);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar alojamentos dos colaboradores:", error);
    throw new Error(`Erro ao buscar alojamentos dos colaboradores: ${error.message}`);
  }

  // Formatar os dados para incluir os nomes dos colaboradores, alojamentos e número do quarto
  return (data || []).map(item => {
    // Garantir que item é do tipo esperado com as propriedades necessárias
    const typedItem = item as unknown as CollaboratorAccommodation & {
      hotel_rooms?: { room_number: string };
      collaborators?: { full_name: string; role?: string; registration_number?: string };
      accommodations?: { name: string; type?: string };
    };
    return {
      ...(typedItem as object),
      collaborator_name: typedItem.collaborators?.full_name,
      collaborator_role: typedItem.collaborators?.role,
      collaborator_registration_number: typedItem.collaborators?.registration_number,
      accommodation_name: typedItem.accommodations?.name,
      room_number: typedItem.hotel_rooms?.room_number || typedItem.room_number || ''
    };
  }) as CollaboratorAccommodation[];
}

export async function getAccommodationCollaborators(clubId: number, accommodationId: number): Promise<CollaboratorAccommodation[]> {
  const { data, error } = await supabase
    .from("collaborator_accommodations")
    .select(`
      *,
      collaborators:collaborator_id (full_name, role, registration_number),
      accommodations:accommodation_id (name, type),
      hotel_rooms!hotel_room_id (room_number)
    `)
    .eq("club_id", clubId as any)
    .eq("accommodation_id", accommodationId as any)
    .eq("status", "active" as any);

  if (error) {
    console.error("Erro ao buscar colaboradores do alojamento:", error);
    throw new Error(`Erro ao buscar colaboradores do alojamento: ${error.message}`);
  }

  return (data || []) as unknown as CollaboratorAccommodation[];
}

export async function assignCollaboratorToAccommodation(
  clubId: number,
  collaboratorId: number,
  accommodationId: number,
  details: {
    room_number?: string;
    hotel_room_id?: number;
    check_in_date?: string;
    check_out_date?: string;
    status?: string;
    notes?: string;
  }
): Promise<CollaboratorAccommodation> {
  // Verificar se o colaborador já está associado a este alojamento
  const { data: existingAssignments } = await supabase
    .from("collaborator_accommodations")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("collaborator_id", collaboratorId as any)
    .eq("accommodation_id", accommodationId as any)
    .eq("status", "active" as any);

  if (existingAssignments && existingAssignments.length > 0) {
    throw new Error("O colaborador já está associado a este alojamento");
  }

  // Verificar se o alojamento existe
  const { data: accommodation, error: accommodationError } = await supabase
    .from("accommodations")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("id", accommodationId as any)
    .single();

  if (accommodationError || !accommodation) {
    throw new Error("Alojamento não encontrado");
  }

  // Verificar capacidade se especificada
  if ((accommodation as any).capacity) {
    // Contar colaboradores e jogadores ativos no alojamento
    const [{ data: currentCollaborators }, { data: currentPlayers }] = await Promise.all([
      supabase
        .from("collaborator_accommodations")
        .select("*")
        .eq("club_id", clubId as any)
        .eq("accommodation_id", accommodationId as any)
        .eq("status", "active" as any),
      supabase
        .from("player_accommodations")
        .select("*")
        .eq("club_id", clubId as any)
        .eq("accommodation_id", accommodationId as any)
        .eq("status", "active" as any)
    ]);

    const totalOccupants = (currentCollaborators?.length || 0) + (currentPlayers?.length || 0);

    // Verificar se a capacidade foi atingida
    if (totalOccupants >= (accommodation as any).capacity) {
      throw new Error(`Capacidade máxima do alojamento atingida (${(accommodation as any).capacity} pessoas)`);
    }
  }

  // Criar a associação
  const { data, error } = await supabase
    .from("collaborator_accommodations")
    .insert({
      club_id: clubId,
      collaborator_id: collaboratorId,
      accommodation_id: accommodationId,
      room_number: details.room_number,
      hotel_room_id: details.hotel_room_id,
      check_in_date: details.check_in_date,
      check_out_date: details.check_out_date,
      status: details.status || "active",
      notes: details.notes
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao associar colaborador ao alojamento:", error);
    throw new Error(`Erro ao associar colaborador ao alojamento: ${error.message}`);
  }

  return data as unknown as CollaboratorAccommodation;
}

export async function updateCollaboratorAccommodation(
  clubId: number,
  id: number,
  updates: Partial<CollaboratorAccommodation>
): Promise<CollaboratorAccommodation> {
  const { data, error } = await supabase
    .from("collaborator_accommodations")
    .update(updates as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar alojamento do colaborador ${id}:`, error);
    throw new Error(`Erro ao atualizar alojamento do colaborador: ${error.message}`);
  }

  return data as unknown as CollaboratorAccommodation;
}

export async function removeCollaboratorFromAccommodation(
  clubId: number,
  collaboratorId: number,
  accommodationId: number
): Promise<boolean> {
  const { error } = await supabase
    .from("collaborator_accommodations")
    .delete()
    .eq("club_id", clubId as any)
    .eq("collaborator_id", collaboratorId as any)
    .eq("accommodation_id", accommodationId as any);

  if (error) {
    console.error("Erro ao remover colaborador do alojamento:", error);
    throw new Error(`Erro ao remover colaborador do alojamento: ${error.message}`);
  }

  return true;
}
