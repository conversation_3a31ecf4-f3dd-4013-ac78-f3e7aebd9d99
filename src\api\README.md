# API de Serviços do Front-end

Este diretório centraliza todas as funções de comunicação com a API (ou mocks).
Troque facilmente os dados fake por chamadas reais depois!

- Edite `api.ts` para adicionar/remover serviços.
- Importe funções de `src/api` para consumir dados no front.
- Ao integrar com o back-end, troque apenas a implementação das funções de serviço.
- Exemplos de funções disponíveis: `getPlayers`, `getMatches`, `getSalaries`, `getContracts`, `getFinancialTransactions`, etc.

**Padrão obrigatório:**
Sempre consuma dados via funções de serviço deste diretório. Nunca use mocks locais ou dados hardcoded nos componentes/páginas.
