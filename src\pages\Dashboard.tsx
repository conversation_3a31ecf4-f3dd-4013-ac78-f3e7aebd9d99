import { Calendar, Trophy, Clipboard, Users, TrendingUp, Activity } from "lucide-react";
import { StatCard } from "@/components/dashboard/StatCard";
import { TeamPerformanceChart } from "@/components/dashboard/TeamPerformanceChart";
import { UpcomingEvents } from "@/components/dashboard/UpcomingEvents";
import { SquadStatus } from "@/components/dashboard/SquadStatus";
import { PastGamesWidget } from "@/components/dashboard/PastGamesWidget";
import { ExpiringContractsWidget } from "@/components/dashboard/ExpiringContractsWidget";
import { EvaluationStatusWidget } from "@/components/dashboard/EvaluationStatusWidget";
import { FitnessModal } from "@/components/modals/FitnessModal";
import { useEffect, useState } from "react";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useSeasonStore } from "@/store/useSeasonStore";
import { getUpcomingMatches, getMatchHistory } from "@/api";
import type { UpcomingMatch, MatchHistory } from "@/api";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useNotificationsStore } from "@/store/useNotificationsStore";
import { getAgendaEvents } from "@/api/api";
import { getPlayerUpcomingTrainings, getUpcomingTrainingsBySeasonAndCategory } from "@/api/trainings";
import type { Training } from "@/api/trainings";
import { supabase } from "@/integrations/supabase/client";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { usePermission } from "@/hooks/usePermission";
import { Badge } from "@/components/ui/badge";
import { useDashboardStats } from "@/hooks/useDashboardStats";

export default function Dashboard() {
  const [upcomingMatches, setUpcomingMatches] = useState<UpcomingMatch[]>([]);
  const [matchHistory, setMatchHistory] = useState<MatchHistory[]>([]);
  const [upcomingTrainings, setUpcomingTrainings] = useState<Training[]>([]);
  const { players, loading: loadingPlayers, error, fetchPlayers } = usePlayersStore();
  const [loading, setLoading] = useState(true);
  const [dashboardError, setDashboardError] = useState<string | null>(null);
  const [isPlayerAccount, setIsPlayerAccount] = useState(false);
  const [playerId, setPlayerId] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>(undefined);
  const [selectedCategoryName, setSelectedCategoryName] = useState<string | null>(null);
  const [fitnessModalOpen, setFitnessModalOpen] = useState(false);

  const clubId = useCurrentClubId();
  const { activeSeason } = useSeasonStore();
  const { user } = useUser();
  const { createNotification } = useNotificationsStore();
  const { categories } = useCategoriesStore();
  const { role, isLoaded } = usePermission();

  // Use dashboard stats hook
  const { stats: dashboardStats, loading: loadingStats } = useDashboardStats(clubId, selectedCategoryId);

  // Verificar se o usuário é um jogador
  useEffect(() => {
    if (!user?.id || !clubId) return;

    async function checkIfPlayerAccount() {
      try {
        // Buscar jogador pelo user_id
        const { data: playerData } = await supabase
          .from("players")
          .select("id")
          .eq("user_id", user.id)
          .eq("club_id", clubId)
          .single();

        if (playerData) {
          setIsPlayerAccount(true);
          setPlayerId(playerData.id);
        } else {
          setIsPlayerAccount(false);
          setPlayerId(null);
        }
      } catch (error) {
        console.error("Erro ao verificar conta de jogador:", error);
        setIsPlayerAccount(false);
      }
    }

    checkIfPlayerAccount();
  }, [user?.id, clubId]);

  // Verificar categoria selecionada e escutar mudanças
  useEffect(() => {
    const updateSelectedCategory = () => {
      const storedCategoryId = localStorage.getItem('selectedCategoryId');
      if (storedCategoryId) {
        const categoryId = parseInt(storedCategoryId);
        setSelectedCategoryId(categoryId);

        // Buscar nome da categoria
        const category = categories.find(c => c.id === categoryId);
        if (category) {
          setSelectedCategoryName(category.name);
        } else {
          setSelectedCategoryName(null);
        }
      } else {
        setSelectedCategoryId(undefined);
        setSelectedCategoryName(null);
      }
    };

    // Atualizar categoria inicialmente
    updateSelectedCategory();

    // Escutar mudanças no localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "selectedCategoryId") {
        updateSelectedCategory();
      }
    };

    // Escutar o evento personalizado de mudança de categoria
    const handleCategoryChanged = (e: CustomEvent) => {
      updateSelectedCategory();
    };

    // Criar um observador para monitorar mudanças no localStorage em tempo real
    const checkForChanges = setInterval(() => {
      const currentCategoryId = localStorage.getItem("selectedCategoryId");
      const currentSelectedId = selectedCategoryId ? selectedCategoryId.toString() : null;

      if (currentCategoryId !== currentSelectedId) {
        updateSelectedCategory();
      }
    }, 500);

    // Adicionar event listeners
    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("categoryChanged", handleCategoryChanged as EventListener);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("categoryChanged", handleCategoryChanged as EventListener);
      clearInterval(checkForChanges);
    };
  }, [categories, selectedCategoryId]);

  // Buscar treinos (para jogadores ou colaboradores)
  useEffect(() => {
    if (!clubId) return;

    async function fetchTrainings() {
      try {
        if (isPlayerAccount && playerId) {
          // Para jogadores: buscar apenas treinos da categoria do jogador
          const trainings = await getPlayerUpcomingTrainings(clubId, playerId);
          setUpcomingTrainings(trainings);
        } else if (activeSeason) {
          // Para colaboradores: buscar treinos da temporada e categoria selecionada
          const trainings = await getUpcomingTrainingsBySeasonAndCategory(
            clubId,
            activeSeason.id,
            selectedCategoryId && selectedCategoryId > 0 ? selectedCategoryId : undefined
          );
          setUpcomingTrainings(trainings);
        }
      } catch (error) {
        console.error("Erro ao buscar treinos:", error);
      }
    }

    fetchTrainings();
  }, [isPlayerAccount, playerId, clubId, activeSeason, selectedCategoryId]);

  useEffect(() => {
    let cancelled = false;
    async function fetchDashboardData() {
      if (!clubId || !activeSeason) {
        setDashboardError("Nenhum clube ou temporada selecionado.");
        setLoading(false);
        return;
      }
      setLoading(true);
      setDashboardError(null);
      try {
        const [upcomingData, historyData] = await Promise.all([
          getUpcomingMatches(clubId, activeSeason.id, selectedCategoryId && selectedCategoryId > 0 ? selectedCategoryId : undefined),
          getMatchHistory(clubId, activeSeason.id, selectedCategoryId && selectedCategoryId > 0 ? selectedCategoryId : undefined)
        ]);
        if (!cancelled) {
          setUpcomingMatches(upcomingData);
          setMatchHistory(historyData);
        }
        await fetchPlayers(clubId);

        // --- NOTIFICAÇÕES DE EVENTOS DO DIA (JOGO/TREINO) ---
        if (user?.id) {
          // Obter data de hoje no formato YYYY-MM-DD
          const today = new Date().toISOString().slice(0, 10);
          // Buscar notificações já criadas para o usuário hoje
          const userNotifications = await import("@/api/api").then(api => api.getUserNotifications(user.id));
          const todayNotifications = userNotifications.filter(n => n.created_at.slice(0, 10) === today);

          // Buscar jogos futuros e treinos
          const [upcomingMatchesToday, trainingsToday] = [
            upcomingData.filter(match => match.date.slice(0, 10) === today),
            (await import("@/api/api")).getTrainings ? (await (await import("@/api/api")).getTrainings(clubId)).filter(training => training.date.slice(0, 10) === today && training.status !== "concluído") : []
          ];

          // Notificação de jogo do dia
          if (upcomingMatchesToday.length > 0) {
            const alreadyNotified = todayNotifications.some(n => n.title === "Jogo hoje" && n.created_at.slice(0, 10) === today);
            if (!alreadyNotified) {
              await createNotification({
                user_id: user.id,
                title: "Jogo hoje",
                description: `Você tem um jogo agendado para hoje pelo clube!`,
              });
            }
          }

          // Notificação de treino do dia
          if (trainingsToday.length > 0) {
            const alreadyNotified = todayNotifications.some(n => n.title === "Treino hoje" && n.created_at.slice(0, 10) === today);
            if (!alreadyNotified) {
              await createNotification({
                user_id: user.id,
                title: "Treino hoje",
                description: `Você tem um treino agendado para hoje pelo clube!`,
              });
            }
          }
        }
        // --- FIM NOTIFICAÇÕES DE EVENTOS DO DIA ---

      } catch (err: any) {
        if (!cancelled) setDashboardError(err?.message || "Erro ao carregar dados do dashboard.");
      } finally {
        if (!cancelled) setLoading(false);
      }
    }
    fetchDashboardData();
    return () => { cancelled = true; };
  }, [fetchPlayers, clubId, activeSeason, user?.id, createNotification, selectedCategoryId]);

  // DEBUG: Exibir players.length e erro
  const debugPlayers = players ? players.length : "null";
  const debugError = error ? error : "nenhum";

  // Média de condição física dos jogadores
  const fitnessValues = players.map((p) => p.stats?.minutes ? p.stats.minutes : null).filter((v): v is number => v !== null);
  const avgFitness = fitnessValues.length > 0 ? Math.round(fitnessValues.reduce((a, b) => a + b, 0) / fitnessValues.length) : 0;

  if (dashboardError) {
    return <div className="text-center text-destructive font-medium mt-8">{dashboardError}</div>;
  }

  // Renderização condicional baseada no tipo de usuário
  const renderDashboard = () => {
    // Dashboard para jogadores
    if (isLoaded && role === "player") {
      return (
        <div className="space-y-4 sm:space-y-6">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Visão geral das suas atividades
            </p>
          </div>

          {/* Stats section para jogadores */}
          <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            <StatCard
              title="Próxima Partida"
              value={upcomingMatches.length > 0 ? upcomingMatches[0].date : "Sem partidas"}
              description={upcomingMatches.length > 0 ? `vs. ${upcomingMatches[0].opponent}` : "Nenhuma partida agendada"}
              icon={Calendar}
            />
            <StatCard
              title="Próximo Treino"
              value={upcomingTrainings.length > 0 ? upcomingTrainings[0].date : "Sem treinos"}
              description={upcomingTrainings.length > 0 ? upcomingTrainings[0].name : "Nenhum treino agendado"}
              icon={Activity}
            />
            <StatCard
              title="Condição Física"
              value={playerId && players.find(p => p.id === playerId)?.stats?.minutes ?
                `${players.find(p => p.id === playerId)?.stats?.minutes}%` : "Sem dados"}
              icon={TrendingUp}
            />
          </div>

          {/* Upcoming events para jogadores */}
          <div className="w-full overflow-hidden">
            <UpcomingEvents
              upcomingMatches={upcomingMatches}
              upcomingTrainings={upcomingTrainings}
            />
          </div>
        </div>
      );
    }

    // Dashboard para médicos
    else if (isLoaded && role === "medical") {
      return (
        <div className="space-y-4 sm:space-y-6">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Visão geral do departamento médico
            </p>
          </div>

          {/* Stats section para médicos */}
          <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            <StatCard
              title="Total de Jogadores"
              value={dashboardStats?.totalActivePlayers?.toString() || players.length.toString()}
              description="Jogadores ativos no elenco"
              icon={Users}
            />
            <StatCard
              title="Próxima Partida"
              value={upcomingMatches.length > 0 ? upcomingMatches[0].date : "Sem partidas"}
              description={upcomingMatches.length > 0 ? `vs. ${upcomingMatches[0].opponent}` : "Nenhuma partida agendada"}
              icon={Calendar}
            />
            <StatCard
              title="Status dos Atletas"
              value={dashboardStats?.playersInRehabilitation?.toString() || "0"}
              description={`${dashboardStats?.playersInRehabilitation || 0} atletas em reabilitação`}
              icon={Activity}
              clickable={true}
              onClick={() => setFitnessModalOpen(true)}
            />
          </div>

          {/* Squad status para médicos */}
          <div className="grid gap-3 sm:gap-4">
            <SquadStatus />
          </div>

          {/* Upcoming events para médicos */}
          <div className="w-full overflow-hidden">
            <UpcomingEvents
              upcomingMatches={upcomingMatches}
              upcomingTrainings={upcomingTrainings}
            />
          </div>
        </div>
      );
    }

    // Dashboard padrão para outros usuários
    else {
      return (
        <div className="space-y-4 sm:space-y-6">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Dashboard</h1>
            {selectedCategoryName ? (
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-1">
                <p className="text-sm sm:text-base text-muted-foreground">
                  Visão geral do seu time e atividades recentes
                </p>
                <Badge variant="outline" className="w-fit">
                  Categoria: {selectedCategoryName}
                </Badge>
              </div>
            ) : (
              <p className="text-sm sm:text-base text-muted-foreground">
                Visão geral do seu time e atividades recentes
              </p>
            )}
          </div>

          {/* Stats section */}
          <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            <StatCard
              title={selectedCategoryName ? `Jogadores - ${selectedCategoryName}` : "Total de Jogadores"}
              value={dashboardStats?.totalActivePlayers?.toString() || players.length.toString()}
              description={selectedCategoryName ? `Jogadores ativos na categoria ${selectedCategoryName}` : "Jogadores ativos no elenco"}
              icon={Users}
            />
            <StatCard
              title={selectedCategoryName ? `Vitórias - ${selectedCategoryName}` : "Vitórias na Temporada"}
              value={matchHistory.length > 0 ? matchHistory.filter((match) => match.result === "win").length.toString() : "0"}
              trend="up"
              icon={Trophy}
              description={selectedCategoryName ? `Vitórias da categoria ${selectedCategoryName}` : "Vitórias na temporada atual"}
            />
            <StatCard
              title="Próxima Partida"
              value={upcomingMatches.length > 0 ? upcomingMatches[0].date : "Sem partidas"}
              description={upcomingMatches.length > 0 ? `vs. ${upcomingMatches[0].opponent}` : "Nenhuma partida agendada"}
              icon={Calendar}
            />
            <StatCard
              title="Status dos Atletas"
              value={dashboardStats?.playersInRehabilitation?.toString() || "0"}
              description={`${dashboardStats?.playersInRehabilitation || 0} atletas em reabilitação`}
              icon={Activity}
              clickable={true}
              onClick={() => setFitnessModalOpen(true)}
            />
          </div>

          {/* Charts section */}
          <div className="grid gap-3 sm:gap-4 grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
            <div className="lg:col-span-1 xl:col-span-1">
              <TeamPerformanceChart players={players} matchHistory={matchHistory} />
            </div>
            <div className="lg:col-span-1 xl:col-span-1">
              <PastGamesWidget matchHistory={matchHistory} />
            </div>
            <div className="lg:col-span-2 xl:col-span-1 space-y-3 sm:space-y-4">
              <ExpiringContractsWidget players={players} />
              <EvaluationStatusWidget />
              <SquadStatus />
            </div>
          </div>

          {/* Upcoming events */}
          <div className="w-full overflow-hidden">
            <UpcomingEvents
              upcomingMatches={upcomingMatches}
              upcomingTrainings={upcomingTrainings}
            />
          </div>
        </div>
      );
    }
  };

  return (
    <>
      {renderDashboard()}

      {/* Fitness Modal */}
      <FitnessModal
        open={fitnessModalOpen}
        onOpenChange={setFitnessModalOpen}
        selectedCategoryId={selectedCategoryId}
      />
    </>
  );
}
