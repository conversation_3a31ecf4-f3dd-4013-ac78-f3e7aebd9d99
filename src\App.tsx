import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import Dashboard from "./pages/Dashboard";
import Elenco from "./pages/Elenco";
import Partidas from "./pages/Partidas";
import JogosPassados from "./pages/JogosPassados";
import NotFound from "./pages/NotFound";
import Treinamentos from "./pages/Treinamentos";
import Agenda from "./pages/Agenda";
import BaseJuvenil from "./pages/BaseJuvenil";
import EscalacaoJogadores from "./pages/EscalacaoJogadores";
import Medico from "./pages/Medico";
import MedicosCadastro from "./pages/MedicosCadastro";
import Financeiro from "./pages/Financeiro";
import PerfilJogador from "./pages/PerfilJogador";
import PlayerStatisticsPage from "./pages/PlayerStatisticsPage";
import PlayerMedicalHistoryPage from "./pages/PlayerMedicalHistoryPage";
import ConfiguracoesClube from "@/pages/ConfiguracoesClube";
import Login from "@/pages/Login";
// import Register from "@/pages/Register";
import AcceptInvitation from "@/pages/AcceptInvitation";
import EstatisticasTemporada from "@/pages/EstatisticasTemporada";
import LandingPage from "@/pages/LandingPage";
import Adversarios from "@/pages/Adversarios";
import Competicoes from "@/pages/Competicoes";
import Alojamentos from "@/pages/Alojamentos";
import Categorias from "@/pages/Categorias";
import Departamentos from "@/pages/Departamentos";
import Usuarios from "@/pages/Usuarios";
import PerfilUsuario from "@/pages/PerfilUsuario";
import Relatorios from "@/pages/Relatorios";
import AuditLogs from "@/pages/AuditLogs";
import Convocacao from "@/pages/Convocacao";
import ConvocacaoDetalhes from "@/pages/ConvocacaoDetalhes";
import Administrativo from "@/pages/Administrativo";
import Estoque from "@/pages/Estoque";
import EmailTest from "@/pages/EmailTest";
import Avaliacao from "./pages/Avaliacao";
import EvaluationRegistration from "./pages/EvaluationRegistration";
import EvaluationSuccess from "./pages/EvaluationSuccess";
import FormTemplates from "./pages/FormTemplates";
import { ThemeProvider } from "@/context/ThemeContext";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { PermissionGuard } from "@/components/PermissionGuard";
import { PlayerProfileGuard } from "@/components/PlayerProfileGuard";
import { PublicRoute } from "@/components/PublicRoute";
import { ClubProvider } from "@/context/ClubContext";
import { UserProvider } from "@/context/UserContext";
import { ClubInfoLoader } from "@/components/ClubInfoLoader";
import { checkAndUpdateLoanedPlayers } from "@/utils/loanManager";
// import { cleanupExpiredTrainings } from "@/api/trainingCleanup"; // Funcionalidade removida
import "@/styles/theme.css";
import "@/styles/player-card.css";

const queryClient = new QueryClient();

function getInitialClubId() {
  const clubId = localStorage.getItem("clubId");
  return clubId ? Number(clubId) : undefined;
}

function ClubProtectedRoutes() {
  const [clubId, setClubId] = useState<number | undefined>(getInitialClubId());

  useEffect(() => {
    function handleStorage() {
      setClubId(getInitialClubId());
    }
    window.addEventListener("storage", handleStorage);
    return () => window.removeEventListener("storage", handleStorage);
  }, []);

  // Verificar jogadores emprestados cujo empréstimo terminou
  useEffect(() => {
    if (clubId) {
      // Verificar uma vez ao carregar a aplicação
      checkAndUpdateLoanedPlayers(clubId).then(count => {
        if (count > 0) {
          console.log(`${count} jogador(es) retornaram automaticamente de empréstimo.`);
        }
      });

      // Configurar verificação periódica (a cada 12 horas) apenas para jogadores emprestados
      const interval = setInterval(() => {
        // Verificar jogadores emprestados
        checkAndUpdateLoanedPlayers(clubId).then(count => {
          if (count > 0) {
            console.log(`${count} jogador(es) retornaram automaticamente de empréstimo.`);
          }
        });
      }, 12 * 60 * 60 * 1000); // 12 horas em milissegundos

      return () => clearInterval(interval);
    }
  }, [clubId]);

  if (!clubId) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-team-blue to-team-blue/60">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
          <h2 className="text-2xl font-bold mb-4 text-team-blue">Faça login para continuar</h2>
          <p className="mb-4">Seu acesso expirou ou você ainda não fez login.<br/>Por favor, <a href="/login" className="text-team-blue underline">faça login</a> para acessar o sistema.</p>
        </div>
      </div>
    );
  }

  return (
    <ClubProvider clubId={clubId}>
      <ClubInfoLoader />
      <Routes>
        <Route element={<Layout />}>
          {/* Rotas acessíveis a todos os usuários autenticados */}
          <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
          <Route path="/perfil" element={
            <ProtectedRoute>
              <PermissionGuard permission="users.view">
                <PerfilUsuario />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          {/* Rotas que exigem permissões específicas */}
          <Route path="/elenco" element={
            <ProtectedRoute>
              <PermissionGuard permission="players.view">
                <Elenco />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/partidas" element={
            <ProtectedRoute>
              <PermissionGuard permission="matches.view">
                <Partidas />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/jogos-passados" element={
            <ProtectedRoute>
              <PermissionGuard permission="matches.view">
                <JogosPassados />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/adversarios" element={
            <ProtectedRoute>
              <PermissionGuard permission="matches.view">
                <Adversarios />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/competicoes" element={
            <ProtectedRoute>
              <PermissionGuard permission="matches.view">
                <Competicoes />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/alojamentos" element={
            <ProtectedRoute>
              <PermissionGuard permission="accommodations.view">
                <Alojamentos />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/treinamentos" element={
            <ProtectedRoute>
              <PermissionGuard permission="trainings.view">
                <Treinamentos />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/agenda" element={
            <ProtectedRoute>
              <PermissionGuard permission="agenda.view">
                <Agenda />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/categorias" element={
            <ProtectedRoute>
              <PermissionGuard permission="categories.view">
                <Categorias />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/departamentos" element={
            <ProtectedRoute>
              <PermissionGuard permission="departments.view">
                <Departamentos />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/usuarios" element={
            <ProtectedRoute>
              <PermissionGuard permission="users.view">
                <Usuarios />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/escalacao" element={
            <ProtectedRoute>
              <PermissionGuard permission="matches.lineup">
                <EscalacaoJogadores />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/medico" element={
            <ProtectedRoute>
              <PermissionGuard permission="medical.view">
                <Medico />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/medicos/cadastro" element={
            <ProtectedRoute>
              <PermissionGuard permissions={["medical_professionals.create", "medical_professionals.edit_own"]}>
                <MedicosCadastro />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/medicos/editar/:id" element={
            <ProtectedRoute>
              <PermissionGuard permissions={["medical_professionals.edit", "medical_professionals.edit_own"]}>
                <MedicosCadastro />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/financeiro" element={
            <ProtectedRoute>
              <PermissionGuard permission="finances.view">
                <Financeiro />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/jogador/:id" element={
            <ProtectedRoute>
              <PlayerProfileGuard>
                <PerfilJogador />
              </PlayerProfileGuard>
            </ProtectedRoute>
          } />

          <Route path="/jogador/:id/estatisticas" element={
            <ProtectedRoute>
              <PlayerProfileGuard>
                <PlayerStatisticsPage />
              </PlayerProfileGuard>
            </ProtectedRoute>
          } />

          <Route path="/jogador/:id/historico-medico" element={
            <ProtectedRoute>
              <PlayerProfileGuard>
                <PlayerMedicalHistoryPage />
              </PlayerProfileGuard>
            </ProtectedRoute>
          } />

          <Route path="/estatisticas" element={
            <ProtectedRoute>
              <PermissionGuard permission="statistics.view">
                <EstatisticasTemporada />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/relatorios" element={
            <ProtectedRoute>
              <PermissionGuard permission="reports.view">
                <Relatorios />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/analytics" element={
            <ProtectedRoute>
              <PermissionGuard permission="analytics.view">
                <ComingSoon title="Analytics" />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/comunicacao" element={
            <ProtectedRoute>
              <PermissionGuard permission="communication.view">
                <ComingSoon title="Comunicação" />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/configuracoes" element={
            <ProtectedRoute>
              <PermissionGuard permission="settings.view">
                <ConfiguracoesClube />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/audit-logs" element={
            <ProtectedRoute>
              <PermissionGuard permission="audit_logs.view">
                <AuditLogs />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/administrativo" element={
            <ProtectedRoute>
              <Administrativo />
            </ProtectedRoute>
          } />

          <Route path="/estoque" element={
            <ProtectedRoute>
              <PermissionGuard permission="inventory.view">
                <Estoque />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/test-email" element={
            <ProtectedRoute>
              <EmailTest />
            </ProtectedRoute>
          } />

          <Route path="/convocacao" element={
            <ProtectedRoute>
              <PermissionGuard permission="callups.view">
                <Convocacao />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/convocacao/:id" element={
            <ProtectedRoute>
              <PermissionGuard permission="callups.view">
                <ConvocacaoDetalhes />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/avaliacao" element={
            <ProtectedRoute>
              <PermissionGuard permission="players.view">
                <Avaliacao />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/fichas" element={
            <ProtectedRoute>
              <PermissionGuard permission="form_templates.view">
                <FormTemplates />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="*" element={<NotFound />} />
        </Route>
      </Routes>
    </ClubProvider>
  );
}

function App() {
  // Verificar e limpar dados de autenticação inválidos ao carregar a aplicação
  useEffect(() => {
    const token = localStorage.getItem("token");

    // Se não há token, mas há outros dados de autenticação, limpar tudo
    if (!token && (localStorage.getItem("userId") || localStorage.getItem("clubId"))) {
      localStorage.removeItem("userId");
      localStorage.removeItem("clubId");
      localStorage.removeItem("selectedCategoryId");
    }

    // Se há token, verificar se é válido
    if (token) {
      try {
        // Decodificar o token (formato: header.payload.signature)
        const payload = JSON.parse(atob(token.split('.')[1]));

        // Verificar se o token expirou
        const expirationTime = payload.exp * 1000; // Converter para milissegundos
        const currentTime = Date.now();

        if (currentTime > expirationTime) {
          // Token expirou, limpar localStorage
          localStorage.removeItem("token");
          localStorage.removeItem("userId");
          localStorage.removeItem("clubId");
          localStorage.removeItem("selectedCategoryId");
        }
      } catch (error) {
        // Token inválido, limpar localStorage
        localStorage.removeItem("token");
        localStorage.removeItem("userId");
        localStorage.removeItem("clubId");
        localStorage.removeItem("selectedCategoryId");
      }
    }
  }, []);

  return (
    <UserProvider>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                {/* Rotas públicas sem Layout/ClubProvider */}
                <Route
                  path="/"
                  element={<LandingPage />}
                />
                <Route
                  path="/login"
                  element={
                    <PublicRoute>
                      <Login />
                    </PublicRoute>
                  }
                />
                {/*  
                <Route
                  path="/register"
                  element={
                    <PublicRoute>
                      <Register />
                    </PublicRoute>
                  }
                /> */}
                <Route
                  path="/accept-invitation"
                  element={
                    <PublicRoute>
                      <AcceptInvitation />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/evaluation-registration"
                  element={
                    <PublicRoute>
                      <EvaluationRegistration />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/evaluation-success"
                  element={
                    <PublicRoute>
                      <EvaluationSuccess />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/email-test"
                  element={<EmailTest />}
                />
                {/* Rotas privadas multi-clube */}
                <Route path="/*" element={<ClubProtectedRoutes />} />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </UserProvider>
  );
}

function ComingSoon({ title }: { title: string }) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
        <h2 className="text-2xl font-bold mb-4 text-team-blue">{title} em breve!</h2>
        <p className="mb-4">Estamos trabalhando para trazer essa funcionalidade para você.</p>
      </div>
    </div>
  );
}

export default App;
