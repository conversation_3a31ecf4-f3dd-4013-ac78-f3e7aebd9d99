import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { PlayerEvaluation } from "@/api/api";
import { formatDate } from "@/utils/formatters";
import { CheckCircle, XCircle, User, Calendar, FileText } from "lucide-react";

interface EvaluationApprovalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  evaluation: PlayerEvaluation;
  actionType: "approve" | "reject";
  userRole: string;
  onSubmit: (notes: string) => void;
}

export function EvaluationApprovalDialog({
  open,
  onOpenChange,
  evaluation,
  actionType,
  userRole,
  onSubmit,
}: EvaluationApprovalDialogProps) {
  const [notes, setNotes] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (actionType === "reject" && !notes.trim()) {
      return; // Require notes for rejection
    }

    setLoading(true);
    try {
      await onSubmit(notes);
      setNotes("");
    } finally {
      setLoading(false);
    }
  };

  const getActionTitle = () => {
    const roleText = userRole === "manager" ? "Gerente" : "Presidente";
    return actionType === "approve" 
      ? `Aprovar como ${roleText}` 
      : `Rejeitar como ${roleText}`;
  };

  const getActionDescription = () => {
    return actionType === "approve"
      ? "Você está prestes a aprovar esta avaliação. Adicione observações se necessário."
      : "Você está prestes a rejeitar esta avaliação. Por favor, explique o motivo da rejeição.";
  };

  const getButtonText = () => {
    return actionType === "approve" ? "Aprovar Avaliação" : "Rejeitar Avaliação";
  };

  const getButtonVariant = () => {
    return actionType === "approve" ? "default" : "destructive";
  };

  const getIcon = () => {
    return actionType === "approve" 
      ? <CheckCircle className="h-5 w-5" />
      : <XCircle className="h-5 w-5" />;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getIcon()}
            {getActionTitle()}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Player Information */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                {evaluation.player_image ? (
                  <AvatarImage src={evaluation.player_image} alt={evaluation.player_name} />
                ) : null}
                <AvatarFallback className="bg-team-blue text-white">
                  {evaluation.player_name?.slice(0, 2).toUpperCase() || "AT"}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold text-lg">{evaluation.player_name}</h3>
                <p className="text-sm text-muted-foreground">
                  {evaluation.position || "Posição não informada"}
                </p>
              </div>
            </div>
            <Badge variant="outline">Pendente</Badge>
          </div>

          <Separator />

          {/* Evaluation Details */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-muted-foreground">Avaliador</p>
                <p className="font-medium">{evaluation.created_by_name || "Não informado"}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-muted-foreground">Data de Criação</p>
                <p className="font-medium">{formatDate(evaluation.created_at)}</p>
              </div>
            </div>
          </div>

          {/* Evaluation Content Preview */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <Label className="text-sm font-medium">Conteúdo da Avaliação</Label>
            </div>
            <div className="bg-muted/50 p-3 rounded-md max-h-32 overflow-y-auto">
              <div 
                className="text-sm prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ 
                  __html: evaluation.content?.substring(0, 500) + (evaluation.content?.length > 500 ? "..." : "") 
                }}
              />
            </div>
          </div>

          <Separator />

          {/* Action Description */}
          <div className="bg-muted/30 p-4 rounded-md">
            <p className="text-sm text-muted-foreground">
              {getActionDescription()}
            </p>
          </div>

          {/* Notes Input */}
          <div className="space-y-2">
            <Label htmlFor="notes">
              {actionType === "approve" ? "Observações (opcional)" : "Motivo da rejeição *"}
            </Label>
            <Textarea
              id="notes"
              placeholder={
                actionType === "approve"
                  ? "Adicione observações sobre a aprovação..."
                  : "Explique o motivo da rejeição..."
              }
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={4}
              className="resize-none"
            />
            {actionType === "reject" && (
              <p className="text-xs text-muted-foreground">
                * Campo obrigatório para rejeições
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button
            variant={getButtonVariant()}
            onClick={handleSubmit}
            disabled={loading || (actionType === "reject" && !notes.trim())}
          >
            {loading ? "Processando..." : getButtonText()}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
