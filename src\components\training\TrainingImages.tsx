import React, { useState } from 'react';
import { TrainingImage } from '@/api/trainings';
import { Di<PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';

interface TrainingImagesProps {
  images: TrainingImage[];
  maxDisplay?: number;
  showViewAll?: boolean;
}

export function TrainingImages({ images, maxDisplay = 3, showViewAll = true }: TrainingImagesProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [showAllImages, setShowAllImages] = useState(false);

  if (!images || images.length === 0) {
    return null;
  }

  const displayImages = showAllImages ? images : images.slice(0, maxDisplay);
  const hasMoreImages = images.length > maxDisplay;

  const openImageModal = (index: number) => {
    setSelectedImageIndex(index);
  };

  const closeImageModal = () => {
    setSelectedImageIndex(null);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (selectedImageIndex === null) return;
    
    const newIndex = direction === 'prev' 
      ? (selectedImageIndex - 1 + images.length) % images.length
      : (selectedImageIndex + 1) % images.length;
    
    setSelectedImageIndex(newIndex);
  };

  return (
    <>
      <div className="mt-3">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-muted-foreground">
            Imagens do treino ({images.length})
          </span>
          {hasMoreImages && showViewAll && !showAllImages && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAllImages(true)}
              className="text-xs h-6 px-2"
            >
              Ver todas
            </Button>
          )}
          {showAllImages && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAllImages(false)}
              className="text-xs h-6 px-2"
            >
              Ver menos
            </Button>
          )}
        </div>
        
        <div className="grid grid-cols-3 gap-2">
          {displayImages.map((image, index) => (
            <div
              key={image.id}
              className="relative aspect-square border rounded-md overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => openImageModal(index)}
            >
              <img
                src={image.image_url}
                alt={`Imagem ${image.image_order + 1} do treino`}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            </div>
          ))}
          
          {hasMoreImages && !showAllImages && (
            <div
              className="relative aspect-square border rounded-md overflow-hidden cursor-pointer bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
              onClick={() => setShowAllImages(true)}
            >
              <span className="text-sm font-medium text-muted-foreground">
                +{images.length - maxDisplay}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Modal para visualização da imagem em tamanho completo */}
      <Dialog open={selectedImageIndex !== null} onOpenChange={closeImageModal}>
        <DialogContent className="max-w-4xl w-full p-0">
          <DialogHeader className="p-4 pb-0">
            <div className="flex items-center justify-between">
              <DialogTitle>
                Imagem {selectedImageIndex !== null ? selectedImageIndex + 1 : 1} de {images.length}
              </DialogTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={closeImageModal}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>
          
          <div className="relative">
            {selectedImageIndex !== null && (
              <div className="relative">
                <img
                  src={images[selectedImageIndex].image_url}
                  alt={`Imagem ${images[selectedImageIndex].image_order + 1} do treino`}
                  className="w-full max-h-[70vh] object-contain"
                />
                
                {images.length > 1 && (
                  <>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => navigateImage('prev')}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 h-10 w-10 p-0 rounded-full"
                    >
                      <ChevronLeft className="h-5 w-5" />
                    </Button>
                    
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => navigateImage('next')}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 h-10 w-10 p-0 rounded-full"
                    >
                      <ChevronRight className="h-5 w-5" />
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>
          
          {images.length > 1 && (
            <div className="p-4 pt-2">
              <div className="flex gap-2 justify-center overflow-x-auto">
                {images.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`flex-shrink-0 w-16 h-16 border-2 rounded-md overflow-hidden ${
                      selectedImageIndex === index ? 'border-primary' : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image.image_url}
                      alt={`Miniatura ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
