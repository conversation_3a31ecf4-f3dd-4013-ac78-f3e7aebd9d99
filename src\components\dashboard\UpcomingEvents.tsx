import { <PERSON><PERSON>con, MapP<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import type { UpcomingMatch } from "@/api/api";
import type { Training } from "@/api/trainings";

interface UpcomingEventsProps {
  upcomingMatches?: UpcomingMatch[];
  upcomingTrainings?: Training[];
}

export function UpcomingEvents({ upcomingMatches = [], upcomingTrainings = [] }: UpcomingEventsProps) {
  // Filtrar treinos passados e concluídos
  const now = new Date();
  const filteredTrainings = upcomingTrainings.filter(training => {
    const trainingDateTime = new Date(`${training.date}T${training.start_time || '00:00'}`);
    return trainingDateTime >= now && training.status !== "concluído";
  });

  // Combinar partidas e treinos em uma única lista de eventos
  const allEvents = [
    ...upcomingMatches.map(match => ({
      id: `match-${match.id}`,
      type: 'match',
      title: match.opponent ? `vs. ${match.opponent}` : "Partida Agendada",
      date: match.date,
      location: match.location,
      time: match.time,
      category: match.categories?.name || (match.category_id ? `Categoria ${match.category_id}` : undefined),
      competition: match.competition,
      data: match
    })),
    ...filteredTrainings.map(training => ({
      id: `training-${training.id}`,
      type: 'training',
      title: training.name,
      date: training.date,
      location: training.location,
      time: training.start_time ?
        (training.end_time ? `${training.start_time} - ${training.end_time}` : training.start_time) :
        (training.time ? training.time.split('-')[0] : ''),
      category: training.category_name,
      trainingType: training.type,
      data: training
    }))
  ];

  // Ordenar eventos por data (mais próximos primeiro)
  const sortedEvents = allEvents.sort((a, b) => {
    const dateA = new Date(`${a.date}T${a.time || '00:00'}`);
    const dateB = new Date(`${b.date}T${b.time || '00:00'}`);
    return dateA.getTime() - dateB.getTime();
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Próximos Eventos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedEvents.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">Nenhum evento futuro agendado.</div>
          ) : (
            sortedEvents.slice(0, 5).map((event) => (
              <div
                key={event.id}
                className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/40 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${
                    event.type === 'match'
                      ? 'bg-green-100 text-team-green'
                      : 'bg-blue-100 text-blue-600'
                  }`}>
                    {event.type === 'match' ? (
                      <CalendarIcon className="h-5 w-5" />
                    ) : (
                      <Dumbbell className="h-5 w-5" />
                    )}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{event.title}</h4>
                      {event.type === 'training' && event.trainingType && (
                        <Badge variant="outline" className={
                          event.trainingType === "físico" ? "border-amber-200 bg-amber-50 text-amber-700" :
                          event.trainingType === "tático" ? "border-primary/20 bg-primary/10 text-primary" :
                          "border-green-200 bg-green-50 text-green-700"
                        }>
                          {event.trainingType}
                        </Badge>
                      )}
                      {event.type === 'match' && event.competition && (
                        <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
                          {event.competition}
                        </Badge>
                      )}
                      {event.category && (
                        <Badge variant="outline" className="border-purple-200 bg-purple-50 text-purple-700">
                          {event.category}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                      <span className="flex items-center gap-1">
                        <CalendarIcon className="h-3 w-3" /> {event.date}
                      </span>
                      {event.time && (
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" /> {event.time}
                        </span>
                      )}
                      {event.location && (
                        <span className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" /> {event.location}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
