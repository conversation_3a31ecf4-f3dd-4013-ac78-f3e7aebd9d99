/**
 * Utility functions for formatting data
 */

import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";

/**
 * Format a date string to a localized format
 * @param dateString Date string to format
 * @param options Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string | null | undefined,
  options: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  }
): string {
  if (!dateString) return "Data não informada";

  try {
    // Use parseISO to avoid timezone issues with date strings in YYYY-MM-DD format
    const date = parseISO(dateString);
    return new Intl.DateTimeFormat("pt-BR", options).format(date);
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Data inválida";
  }
}

/**
 * Format a number as currency
 * @param value Number to format
 * @param currency Currency code
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number | null | undefined,
  currency: string = "BRL"
): string {
  if (value === null || value === undefined) return "Valor não informado";

  try {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency,
    }).format(value);
  } catch (error) {
    console.error("Error formatting currency:", error);
    return "Valor inválido";
  }
}

/**
 * Format a number with thousands separator
 * @param value Number to format
 * @returns Formatted number string
 */
export function formatNumber(value: number | null | undefined): string {
  if (value === null || value === undefined) return "Número não informado";

  try {
    return new Intl.NumberFormat("pt-BR").format(value);
  } catch (error) {
    console.error("Error formatting number:", error);
    return "Número inválido";
  }
}

/**
 * Format a CPF number (Brazilian ID)
 * @param cpf CPF number
 * @returns Formatted CPF string
 */
export function formatCPF(cpf: string | null | undefined): string {
  if (!cpf) return "CPF não informado";

  // Remove non-numeric characters
  const numericCPF = cpf.replace(/\D/g, "");

  if (numericCPF.length !== 11) return cpf;

  return numericCPF.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
}

/**
 * Format a phone number
 * @param phone Phone number
 * @returns Formatted phone string
 */
export function formatPhone(phone: string | null | undefined): string {
  if (!phone) return "Telefone não informado";

  // Remove non-numeric characters
  const numericPhone = phone.replace(/\D/g, "");

  if (numericPhone.length < 10) return phone;

  if (numericPhone.length === 11) {
    return numericPhone.replace(/(\d{2})(\d{1})(\d{4})(\d{4})/, "($1) $2 $3-$4");
  }

  return numericPhone.replace(/(\d{2})(\d{4})(\d{4})/, "($1) $2-$3");
}

/**
 * Format a time string (HH:MM)
 * @param timeString Time string
 * @returns Formatted time string
 */
export function formatTime(timeString: string | null | undefined): string {
  if (!timeString) return "Horário não informado";

  return timeString;
}

/**
 * Truncate player name to fit in PDF while keeping complete words
 * @param name Player full name
 * @param maxLength Maximum number of characters (default: 25)
 * @returns Truncated name with complete words only
 */
export function truncatePlayerName(name: string | null | undefined, maxLength: number = 25): string {
  if (!name) return "Nome não informado";

  // Se o nome já é menor que o limite, retorna como está
  if (name.length <= maxLength) return name;

  // Divide o nome em palavras
  const words = name.split(' ');
  let truncatedName = '';

  // Adiciona palavras até atingir o limite
  for (const word of words) {
    const testName = truncatedName ? `${truncatedName} ${word}` : word;

    // Se adicionar esta palavra ultrapassar o limite, para
    if (testName.length > maxLength) {
      break;
    }

    truncatedName = testName;
  }

  // Se não conseguiu adicionar nenhuma palavra (primeira palavra muito longa),
  // retorna a primeira palavra truncada
  if (!truncatedName && words.length > 0) {
    truncatedName = words[0].substring(0, maxLength);
  }

  return truncatedName || name.substring(0, maxLength);
}
