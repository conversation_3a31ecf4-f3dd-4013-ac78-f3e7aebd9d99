-- Create collaborator_accommodations table
CREATE TABLE IF NOT EXISTS collaborator_accommodations (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  collaborator_id INTEGER REFERENCES collaborators(id) NOT NULL,
  accommodation_id INTEGER REFERENCES accommodations(id) NOT NULL,
  room_number TEXT,
  hotel_room_id INTEGER REFERENCES hotel_rooms(id),
  check_in_date DATE,
  check_out_date DATE,
  status TEXT DEFAULT 'active',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_collaborator_accommodations_club_id ON collaborator_accommodations(club_id);
CREATE INDEX IF NOT EXISTS idx_collaborator_accommodations_collaborator_id ON collaborator_accommodations(collaborator_id);
CREATE INDEX IF NOT EXISTS idx_collaborator_accommodations_accommodation_id ON collaborator_accommodations(accommodation_id);

-- Function to remove all collaborator associations when status becomes 'inactive'
CREATE OR REPLACE FUNCTION remove_collaborator_associations(p_club_id INTEGER, p_collaborator_id INTEGER)
RETURNS VOID AS $$
BEGIN
  -- Log the operation
  RAISE NOTICE 'Removendo vinculações do colaborador % do clube %', p_collaborator_id, p_club_id;

  -- 1. Remove from accommodations (set status to 'completed' instead of deleting for history)
  UPDATE collaborator_accommodations
  SET status = 'completed', check_out_date = CURRENT_DATE
  WHERE club_id = p_club_id AND collaborator_id = p_collaborator_id AND status = 'active';

  -- 2. Deactivate salaries (set status to 'inactive' instead of deleting for history)
  -- Note: This assumes collaborator_salaries table exists or uses financial_data field
  UPDATE collaborators
  SET financial_data = jsonb_set(
    COALESCE(financial_data, '{}'),
    '{status}',
    '"inactive"'
  )
  WHERE club_id = p_club_id AND id = p_collaborator_id;

  -- 3. Cancel pending salary advances
  UPDATE salary_advances
  SET status = 'cancelled'
  WHERE club_id = p_club_id AND person_id = p_collaborator_id::text
  AND person_type = 'collaborator' AND status = 'active';

  -- 4. Remove from agenda events participants (if collaborators are stored in participants)
  UPDATE agenda_events
  SET participants = array_remove(participants, ('collaborator_' || p_collaborator_id)::text)
  WHERE club_id = p_club_id AND participants @> ARRAY[('collaborator_' || p_collaborator_id)::text];

  -- Log completion
  RAISE NOTICE 'Vinculações do colaborador % removidas com sucesso', p_collaborator_id;
END;
$$ LANGUAGE plpgsql;

-- Function to handle collaborator status changes
CREATE OR REPLACE FUNCTION handle_collaborator_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if status changed to 'inactive'
  IF NEW.status = 'inactive' AND (OLD.status IS NULL OR OLD.status != 'inactive') THEN
    -- Remove all associations
    PERFORM remove_collaborator_associations(NEW.club_id, NEW.id);

    -- Log the status change
    RAISE NOTICE 'Colaborador % alterado para status inativo. Vinculações removidas.', NEW.id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically handle collaborator status changes
DROP TRIGGER IF EXISTS trigger_collaborator_status_change ON collaborators;

CREATE TRIGGER trigger_collaborator_status_change
  AFTER UPDATE OF status ON collaborators
  FOR EACH ROW
  EXECUTE FUNCTION handle_collaborator_status_change();

-- Enable RLS for collaborator_accommodations
ALTER TABLE collaborator_accommodations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for collaborator_accommodations
CREATE POLICY "Club members can view their own collaborator accommodations"
  ON collaborator_accommodations
  FOR SELECT
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can insert their own collaborator accommodations"
  ON collaborator_accommodations
  FOR INSERT
  WITH CHECK (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can update their own collaborator accommodations"
  ON collaborator_accommodations
  FOR UPDATE
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can delete their own collaborator accommodations"
  ON collaborator_accommodations
  FOR DELETE
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);
