import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { ClubInfo } from "@/api/api";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';
import type { Training, TrainingGoal, Exercise } from "@/api/api";

// Extend jsPDF type to include autoTable
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: typeof autoTable;
}

export interface TrainingReportData {
  trainings: Training[];
  completedTrainings: Training[];
  goals: TrainingGoal[];
  exercises: Exercise[];
  statistics: {
    totalTrainings: number;
    completedTrainings: number;
    upcomingTrainings: number;
    typeDistribution: Record<string, number>;
    categoryDistribution: Record<string, number>;
  };
}

export interface TrainingReportOptions {
  includeStatistics: boolean;
  includeGoals: boolean;
  includeExercises: boolean;
  includeUpcoming: boolean;
  includeCompleted: boolean;
  period?: {
    year: number;
    month?: number;
  };
  categoryFilter?: string;
  typeFilter?: string;
}

/**
 * Gera um relatório completo de treinamentos em PDF
 * @param data Dados dos treinamentos
 * @param clubInfo Informações do clube
 * @param options Opções do relatório
 * @param filename Nome do arquivo PDF
 */
export async function generateTrainingReport(
  data: TrainingReportData,
  clubInfo: ClubInfo,
  options: TrainingReportOptions,
  filename: string = 'relatorio-treinamentos.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 15;
  let currentY = 20;

  // Função auxiliar para verificar se precisa de nova página
  const checkPageBreak = (neededHeight: number) => {
    if (currentY + neededHeight > pageHeight - 20) {
      doc.addPage();
      currentY = 20;
      addHeader();
    }
  };

  // Função para adicionar cabeçalho
  const addHeader = () => {
    doc.setFontSize(18);
    doc.setFont("helvetica", "bold");
    doc.text('Relatório de Treinamentos', pageWidth / 2, currentY, { align: 'center' });
    currentY += 10;

    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, currentY);
    currentY += 5;

    const periodText = options.period
      ? `Período: ${options.period.month ? getMonthName(options.period.month) + '/' : ''}${options.period.year}`
      : 'Período: Todos os registros';
    doc.text(periodText, margin, currentY);
    currentY += 5;

    doc.text(`Gerado em: ${new Date().toLocaleDateString('pt-BR')}`, margin, currentY);
    currentY += 15;
  };

  // Adicionar cabeçalho inicial
  addHeader();

  // 1. Seção de Estatísticas
  if (options.includeStatistics) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Resumo Estatístico', margin, currentY);
    currentY += 10;

    // Estatísticas gerais
    const stats = data.statistics;
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");

    const statsData = [
      ['Total de Treinamentos', stats.totalTrainings.toString()],
      ['Treinamentos Concluídos', stats.completedTrainings.toString()],
      ['Treinamentos Agendados', stats.upcomingTrainings.toString()],
      ['Taxa de Conclusão', `${stats.totalTrainings > 0 ? Math.round((stats.completedTrainings / stats.totalTrainings) * 100) : 0}%`]
    ];

    autoTable(doc, {
      startY: currentY,
      head: [['Métrica', 'Valor']],
      body: statsData,
      theme: 'grid',
      headStyles: { fillColor: getClubPrimaryColorRgb() },
      margin: { left: margin, right: margin },
      tableWidth: 'auto'
    });

    currentY = (doc as any).lastAutoTable.finalY + 15;

    // Distribuição por tipo
    if (Object.keys(stats.typeDistribution).length > 0) {
      checkPageBreak(40);

      doc.setFontSize(12);
      doc.setFont("helvetica", "bold");
      doc.text('Distribuição por Tipo', margin, currentY);
      currentY += 8;

      const typeData = Object.entries(stats.typeDistribution).map(([type, count]) => [
        type.charAt(0).toUpperCase() + type.slice(1),
        count.toString(),
        `${stats.totalTrainings > 0 ? Math.round((count / stats.totalTrainings) * 100) : 0}%`
      ]);

      autoTable(doc, {
        startY: currentY,
        head: [['Tipo', 'Quantidade', 'Percentual']],
        body: typeData,
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb() },
        margin: { left: margin, right: margin }
      });

      currentY = (doc as any).lastAutoTable.finalY + 15;
    }

    // Distribuição por categoria
    if (Object.keys(stats.categoryDistribution).length > 0) {
      checkPageBreak(40);

      doc.setFontSize(12);
      doc.setFont("helvetica", "bold");
      doc.text('Distribuição por Categoria', margin, currentY);
      currentY += 8;

      const categoryData = Object.entries(stats.categoryDistribution).map(([category, count]) => [
        category || 'Sem categoria',
        count.toString(),
        `${stats.totalTrainings > 0 ? Math.round((count / stats.totalTrainings) * 100) : 0}%`
      ]);

      autoTable(doc, {
        startY: currentY,
        head: [['Categoria', 'Quantidade', 'Percentual']],
        body: categoryData,
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb() },
        margin: { left: margin, right: margin }
      });

      currentY = (doc as any).lastAutoTable.finalY + 15;
    }
  }

  // 2. Seção de Objetivos
  if (options.includeGoals && data.goals.length > 0) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Objetivos de Treinamento', margin, currentY);
    currentY += 10;

    const goalsData = data.goals.map(goal => [
      goal.name,
      goal.type.charAt(0).toUpperCase() + goal.type.slice(1),
      `${goal.current_value}/${goal.target_value}`,
      `${Math.round((goal.current_value / goal.target_value) * 100)}%`,
      goal.description || '-'
    ]);

    autoTable(doc, {
      startY: currentY,
      head: [['Objetivo', 'Tipo', 'Progresso', '%', 'Descrição']],
      body: goalsData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb() },
      margin: { left: margin, right: margin },
      columnStyles: {
        4: { cellWidth: 40 }
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 15;
  }

  // 3. Seção de Treinamentos Agendados
  if (options.includeUpcoming && data.trainings.length > 0) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Treinamentos Agendados', margin, currentY);
    currentY += 10;

    const upcomingData = data.trainings
      .filter(t => t.status !== 'concluído')
      .map(training => [
        training.name,
        training.type.charAt(0).toUpperCase() + training.type.slice(1),
        training.category_name || 'Sem categoria',
        training.date,
        training.time || '-',
        training.location,
        training.coach,
        training.status.charAt(0).toUpperCase() + training.status.slice(1)
      ]);

    if (upcomingData.length > 0) {
      autoTable(doc, {
        startY: currentY,
        head: [['Nome', 'Tipo', 'Categoria', 'Data', 'Horário', 'Local', 'Treinador', 'Status']],
        body: upcomingData,
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb() },
        margin: { left: margin, right: margin },
        styles: { fontSize: 8 }
      });

      currentY = (doc as any).lastAutoTable.finalY + 15;
    } else {
      doc.setFontSize(10);
      doc.setFont("helvetica", "italic");
      doc.text('Nenhum treinamento agendado encontrado.', margin, currentY);
      currentY += 15;
    }
  }

  // 4. Seção de Treinamentos Concluídos
  if (options.includeCompleted && data.completedTrainings.length > 0) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Treinamentos Concluídos', margin, currentY);
    currentY += 10;

    const completedData = data.completedTrainings.map(training => [
      training.name,
      training.type.charAt(0).toUpperCase() + training.type.slice(1),
      training.category_name || 'Sem categoria',
      training.date,
      training.time || '-',
      training.location,
      training.coach,
      training.participants?.toString() || '0'
    ]);

    autoTable(doc, {
      startY: currentY,
      head: [['Nome', 'Tipo', 'Categoria', 'Data', 'Horário', 'Local', 'Treinador', 'Participantes']],
      body: completedData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb() },
      margin: { left: margin, right: margin },
      styles: { fontSize: 8 }
    });

    currentY = (doc as any).lastAutoTable.finalY + 15;
  }

  // 5. Seção de Exercícios Mais Utilizados
  if (options.includeExercises && data.exercises.length > 0) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Banco de Exercícios', margin, currentY);
    currentY += 10;

    const exercisesData = data.exercises.slice(0, 20).map(exercise => [
      exercise.name,
      exercise.category || '-',
      exercise.difficulty || '-',
      exercise.description || '-'
    ]);

    autoTable(doc, {
      startY: currentY,
      head: [['Nome', 'Categoria', 'Dificuldade', 'Descrição']],
      body: exercisesData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb() },
      margin: { left: margin, right: margin },
      columnStyles: {
        3: { cellWidth: 50 }
      },
      styles: { fontSize: 8 }
    });

    currentY = (doc as any).lastAutoTable.finalY + 15;
  }

  // Adicionar rodapé em todas as páginas
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Relatório de Treinamentos`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

// Função auxiliar para obter nome do mês
function getMonthName(month: number): string {
  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ];
  return months[month - 1] || '';
}
