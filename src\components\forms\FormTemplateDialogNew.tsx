import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useFormTemplatesStore } from "@/store/useFormTemplatesStore";
import { ClubFormTemplate } from "@/api/clubFormTemplates";
import { RichTextEditor } from "@/components/ui/rich-text-editor";

interface FormTemplateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  template?: ClubFormTemplate | null;
  onSuccess?: () => void;
}

const FORM_TYPE_OPTIONS = [
  { value: "pre_registration", label: "Ficha de Pré-cadastro" },
  { value: "housing", label: "Ficha de Moradia" },
  { value: "liability_waiver", label: "Termo de Responsabilidade" },
  { value: "custom", label: "Personalizado" }
];

export function FormTemplateDialog({
  open,
  onOpenChange,
  template,
  onSuccess
}: FormTemplateDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const { addTemplate, updateTemplate, loading } = useFormTemplatesStore();

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [content, setContent] = useState("");
  const [formType, setFormType] = useState<string>("custom");
  const [isActive, setIsActive] = useState(true);
  const [error, setError] = useState("");

  const isEditing = !!template;

  // Reset form when dialog opens/closes or template changes
  useEffect(() => {
    if (open) {
      if (template) {
        setName(template.name);
        setDescription(template.description || "");
        setContent(template.content);
        setFormType(template.form_type);
        setIsActive(template.is_active);
      } else {
        setName("");
        setDescription("");
        setContent("");
        setFormType("custom");
        setIsActive(true);
      }
      setError("");
    }
  }, [open, template]);

  const handleSave = async () => {
    if (!name.trim()) {
      setError("O nome é obrigatório.");
      return;
    }

    if (!content.trim()) {
      setError("O conteúdo é obrigatório.");
      return;
    }

    if (!user?.id) {
      setError("Usuário não autenticado.");
      return;
    }

    setError("");

    try {
      if (isEditing && template) {
        await updateTemplate(clubId, template.id, {
          name,
          description,
          content,
          form_type: formType as any,
          is_active: isActive
        }, user.id);

        toast({
          title: "Template atualizado",
          description: "O template foi atualizado com sucesso.",
        });
      } else {
        await addTemplate(clubId, {
          name,
          description,
          content,
          form_type: formType as any,
          is_active: isActive,
          created_by: user.id
        }, user.id);

        toast({
          title: "Template criado",
          description: "O template foi criado com sucesso.",
        });
      }

      onSuccess?.();
    } catch (err) {
      setError(isEditing ? "Erro ao atualizar template." : "Erro ao criar template.");
      toast({
        title: isEditing ? "Erro ao atualizar template" : "Erro ao criar template",
        description: "Ocorreu um erro ao salvar o template.",
        variant: "destructive",
      });
    }
  };

  const getDefaultContent = (type: string) => {
    switch (type) {
      case "pre_registration":
        return `<h2>FICHA DE PRÉ-CADASTRO</h2>
<p><strong>Nome completo:</strong> _________________________________</p>
<p><strong>Data de nascimento:</strong> ___/___/______</p>
<p><strong>CPF:</strong> _________________________________</p>
<p><strong>RG:</strong> _________________________________</p>
<p><strong>Endereço:</strong> _________________________________</p>
<p><strong>Telefone:</strong> _________________________________</p>
<p><strong>Email:</strong> _________________________________</p>
<p><strong>Posição preferida:</strong> _________________________________</p>
<p><strong>Experiência anterior:</strong> _________________________________</p>
<br>
<p>Declaro que as informações acima são verdadeiras.</p>
<br>
<p>Data: ___/___/______</p>
<p>Assinatura: _________________________________</p>`;
      case "housing":
        return `<h2>FICHA DE MORADIA</h2>
<h3>AUTORIZAÇÃO PARA RESIDÊNCIA EM ALOJAMENTO</h3>
<p>Eu, <u>_________________________________</u>, portador(a) do RG nº <u>_______________</u>, CPF nº <u>_______________</u>, residente e domiciliado(a) à <u>_________________________________</u>, na qualidade de responsável legal pelo atleta <u>_________________________________</u>, AUTORIZO sua residência no alojamento do clube.</p>
<h4>CONDIÇÕES GERAIS:</h4>
<ol>
<li>O atleta deverá respeitar as regras de convivência estabelecidas pelo clube;</li>
<li>O atleta deverá manter seus estudos regularmente;</li>
<li>O atleta deverá zelar pela conservação das instalações;</li>
</ol>
<p>Data: ___/___/______</p>
<p>Assinatura do Responsável: _________________________________</p>`;
      case "liability_waiver":
        return `<h2>TERMO DE ISENÇÃO DE RESPONSABILIDADE</h2>
<p>Eu, <u>_________________________________</u>, portador(a) do RG nº <u>_______________</u>, CPF nº <u>_______________</u>, declaro estar ciente dos riscos inerentes à prática esportiva e isento o clube de qualquer responsabilidade por eventuais lesões ou acidentes.</p>
<p>Declaro também estar em plenas condições físicas para a prática do futebol.</p>
<p>Data: ___/___/______</p>
<p>Assinatura: _________________________________</p>`;
      default:
        return `<h2>DOCUMENTO PERSONALIZADO</h2>
<p>Adicione aqui o conteúdo do seu documento personalizado.</p>
<p>Você pode usar formatação rica, incluindo:</p>
<ul>
<li>Texto em <strong>negrito</strong> e <em>itálico</em></li>
<li>Listas numeradas e com marcadores</li>
<li>Diferentes alinhamentos</li>
<li>Cores e destaques</li>
</ul>
<p>Data: ___/___/______</p>
<p>Assinatura: _________________________________</p>`;
    }
  };

  const handleFormTypeChange = (newType: string) => {
    setFormType(newType);
    if (!isEditing && !content.trim()) {
      setContent(getDefaultContent(newType));
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[90vw] sm:max-h-[90vh] w-[90vw] h-[90vh] flex flex-col">
        <DialogHeader className="pb-4 border-b">
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            <span className="w-2 h-2 bg-primary rounded-full"></span>
            {isEditing ? "Editar Template" : "Novo Template"}
          </DialogTitle>
          {error && (
            <div className="mt-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm text-destructive font-medium">{error}</p>
            </div>
          )}
        </DialogHeader>

        <div className="flex-1 overflow-auto space-y-6 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome*</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Nome do template"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="formType">Tipo*</Label>
              <Select value={formType} onValueChange={handleFormTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  {FORM_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Descrição opcional do template"
              rows={2}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              checked={isActive}
              onCheckedChange={(checked) => setIsActive(checked as boolean)}
            />
            <Label htmlFor="isActive" className="cursor-pointer">
              Template ativo (disponível para uso)
            </Label>
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Conteúdo*</Label>
            <div className="border rounded-md">
              <RichTextEditor
                content={content}
                onChange={setContent}
                className="min-h-[400px]"
              />
            </div>
          </div>

        </div>

        <DialogFooter className="pt-4 border-t bg-muted/20">
          <div className="flex gap-3 w-full">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSave}
              disabled={loading}
              className="flex-1 bg-primary hover:bg-primary/90"
            >
              {loading ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Salvando...
                </>
              ) : (
                isEditing ? "Atualizar Template" : "Criar Template"
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
