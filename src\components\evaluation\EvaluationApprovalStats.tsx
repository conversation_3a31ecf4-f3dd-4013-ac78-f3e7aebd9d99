import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import {
  getPendingEvaluationsForApproval,
  PlayerEvaluation
} from "@/api/api";
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Users,
  TrendingUp
} from "lucide-react";

export function EvaluationApprovalStats() {
  const [evaluations, setEvaluations] = useState<PlayerEvaluation[]>([]);
  const [loading, setLoading] = useState(true);
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { role } = usePermission();

  const canApprove = role === "manager" || role === "president";

  useEffect(() => {
    const fetchEvaluations = async () => {
      if (!clubId || !user?.id || !canApprove) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const data = await getPendingEvaluationsForApproval(clubId, user.id);
        setEvaluations(data);
      } catch (error) {
        console.error("Error fetching pending evaluations:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvaluations();
  }, [clubId, user?.id, canApprove]);

  if (!canApprove) {
    return null;
  }

  const getStats = () => {
    const total = evaluations.length;
    const awaitingManager = evaluations.filter(
      e => e.requires_manager_approval && !e.approved_by_manager
    ).length;
    const awaitingPresident = evaluations.filter(
      e => e.requires_president_approval && !e.approved_by_president
    ).length;
    const fullyApproved = evaluations.filter(
      e => e.approved_by_manager && e.approved_by_president
    ).length;

    return {
      total,
      awaitingManager,
      awaitingPresident,
      fullyApproved
    };
  };

  const stats = getStats();

  const getMyPendingCount = () => {
    if (role === "manager") {
      return evaluations.filter(
        e => e.requires_manager_approval && !e.approved_by_manager
      ).length;
    } else if (role === "president") {
      return evaluations.filter(
        e => e.requires_president_approval && !e.approved_by_president
      ).length;
    }
    return 0;
  };

  const myPendingCount = getMyPendingCount();

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Aprovações de Avaliações
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-20">
            <p className="text-muted-foreground">Carregando...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5" />
          Aprovações de Avaliações
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* My Pending Approvals */}
          {myPendingCount > 0 && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    Aguardando sua aprovação
                  </span>
                </div>
                <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                  {myPendingCount}
                </Badge>
              </div>
            </div>
          )}

          {/* Overall Stats */}
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-3 bg-blue-50 rounded-md">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="text-lg font-bold text-blue-600">{stats.total}</span>
              </div>
              <p className="text-xs text-blue-600">Total Pendentes</p>
            </div>

            <div className="text-center p-3 bg-green-50 rounded-md">
              <div className="flex items-center justify-center gap-1 mb-1">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-lg font-bold text-green-600">{stats.fullyApproved}</span>
              </div>
              <p className="text-xs text-green-600">Totalmente Aprovadas</p>
            </div>

            <div className="text-center p-3 bg-orange-50 rounded-md">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Clock className="h-4 w-4 text-orange-600" />
                <span className="text-lg font-bold text-orange-600">{stats.awaitingManager}</span>
              </div>
              <p className="text-xs text-orange-600">Aguardando Gerente</p>
            </div>

            <div className="text-center p-3 bg-purple-50 rounded-md">
              <div className="flex items-center justify-center gap-1 mb-1">
                <TrendingUp className="h-4 w-4 text-purple-600" />
                <span className="text-lg font-bold text-purple-600">{stats.awaitingPresident}</span>
              </div>
              <p className="text-xs text-purple-600">Aguardando Presidente</p>
            </div>
          </div>

          {/* Recent Evaluations */}
          {evaluations.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Avaliações Recentes</h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {evaluations.slice(0, 3).map((evaluation) => (
                  <div
                    key={evaluation.id}
                    className="flex items-center justify-between p-2 text-sm border rounded-md hover:bg-muted/50 cursor-pointer"
                    onClick={() => window.location.href = `/jogador/${evaluation.player_id}`}
                  >
                    <div>
                      <p className="font-medium">{evaluation.player_name}</p>
                      <p className="text-xs text-muted-foreground">
                        {evaluation.position || "Posição não informada"}
                      </p>
                    </div>
                    <div className="flex items-center gap-1">
                      {evaluation.approved_by_manager ? (
                        <CheckCircle className="h-3 w-3 text-green-500" />
                      ) : (
                        <Clock className="h-3 w-3 text-orange-500" />
                      )}
                      {evaluation.approved_by_president ? (
                        <CheckCircle className="h-3 w-3 text-green-500" />
                      ) : (
                        <Clock className="h-3 w-3 text-purple-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {stats.total === 0 && (
            <div className="text-center py-4">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                Nenhuma avaliação pendente de aprovação
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
