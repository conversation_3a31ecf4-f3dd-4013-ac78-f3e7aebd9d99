import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Tipos
export type Accommodation = Database["public"]["Tables"]["accommodations"]["Row"];
export type HotelRoom = Database["public"]["Tables"]["hotel_rooms"]["Row"];
export type PlayerAccommodation = Database["public"]["Tables"]["player_accommodations"]["Row"] & {
  player_name?: string;
  accommodation_name?: string;
  player_nickname?: string;
  player_birthdate?: string;
  player_entry_date?: string;
  room_number?: string;
  // Add these properties to fix TypeScript errors
  players?: {
    name: string;
    nickname?: string;
    birthdate?: string;
    entry_date?: string;
  };
  accommodations?: {
    name: string;
    type?: string;
  };
  hotel_rooms?: {
    room_number: string;
  };
};



// Funções para gerenciar alojamentos
export async function getAccommodations(clubId: number): Promise<Accommodation[]> {
  const { data, error } = await supabase
    .from("accommodations")
    .select("*")
    .eq("club_id", clubId as any)
    .order("name");

  if (error) {
    console.error("Erro ao buscar alojamentos:", error);
    throw new Error(`Erro ao buscar alojamentos: ${error.message}`);
  }

  return (data || []) as unknown as Accommodation[];
}

export async function getAccommodationById(clubId: number, id: number): Promise<Accommodation> {
  const { data, error } = await supabase
    .from("accommodations")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .single();

  if (error) {
    console.error(`Erro ao buscar alojamento ${id}:`, error);
    throw new Error(`Erro ao buscar alojamento: ${error.message}`);
  }

  if (!data) {
    throw new Error(`Alojamento ${id} não encontrado`);
  }

  return data as unknown as Accommodation;
}

export async function createAccommodation(
  clubId: number,
  accommodation: Omit<Accommodation, "id" | "club_id" | "created_at">,
  rooms?: { room_number: string; capacity: number }[]
): Promise<Accommodation> {
  // Iniciar uma transação para garantir que tudo seja criado corretamente
  const { data, error } = await supabase
    .from("accommodations")
    .insert({
      ...accommodation,
      club_id: clubId
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar alojamento:", error);
    throw new Error(`Erro ao criar alojamento: ${error.message}`);
  }

  // Se for um hotel e tiver quartos definidos, criar os quartos
  if (accommodation.type === "hotel" && rooms && rooms.length > 0 && data) {
    try {
      // Importar a função de forma dinâmica para evitar dependência circular
      const { createMultipleRooms } = await import("./hotelRooms");

      // Converter data para unknown primeiro e depois para Accommodation
      const accommodationData = data as unknown as Accommodation;
      if (accommodationData && typeof accommodationData.id === 'number') {
        await createMultipleRooms(clubId, accommodationData.id, rooms);
      }
    } catch (roomError) {
      console.error("Erro ao criar quartos do hotel:", roomError);
      // Não vamos falhar a criação do alojamento se os quartos falharem
      // Apenas logamos o erro para depuração
    }
  }

  return data as unknown as Accommodation;
}

export async function updateAccommodation(
  clubId: number,
  id: number,
  accommodation: Partial<Accommodation>,
  rooms?: { id?: number; room_number: string; capacity: number }[]
): Promise<Accommodation> {
  const { data, error } = await supabase
    .from("accommodations")
    .update(accommodation as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar alojamento ${id}:`, error);
    throw new Error(`Erro ao atualizar alojamento: ${error.message}`);
  }

  // Se for um hotel e tiver quartos definidos, atualizar os quartos
  if (accommodation.type === "hotel" && rooms && rooms.length > 0 && data) {
    try {
      // Importar as funções de forma dinâmica para evitar dependência circular
      const { createHotelRoom, updateHotelRoom } = await import("./hotelRooms");

      // Para cada quarto na lista
      for (const room of rooms) {
        if (room.id) {
          // Se tem ID, é um quarto existente, atualizar
          await updateHotelRoom(clubId, room.id, {
            room_number: room.room_number,
            capacity: room.capacity
          });
        } else {
          // Se não tem ID, é um novo quarto, criar
          await createHotelRoom(clubId, id, {
            room_number: room.room_number,
            capacity: room.capacity
          });
        }
      }
    } catch (roomError) {
      console.error("Erro ao atualizar quartos do hotel:", roomError);
      // Não vamos falhar a atualização do alojamento se os quartos falharem
      // Apenas logamos o erro para depuração
    }
  }

  return data as unknown as Accommodation;
}

export async function deleteAccommodation(clubId: number, id: number): Promise<{success: boolean, playersRemoved: number}> {
  // Primeiro, verificar se há jogadores associados a este alojamento
  const { data: playerAccommodations, error: playerError } = await supabase
    .from("player_accommodations")
    .select("id")
    .eq("accommodation_id", id as any)
    .eq("club_id", clubId as any);

  if (playerError) {
    console.error(`Erro ao verificar jogadores do alojamento ${id}:`, playerError);
    throw new Error(`Erro ao verificar jogadores do alojamento: ${playerError.message}`);
  }

  // Contar quantos jogadores estão associados
  const playersCount = playerAccommodations ? playerAccommodations.length : 0;

  // Se houver jogadores associados, remover todas as associações
  if (playersCount > 0) {
    const { error: removeError } = await supabase
      .from("player_accommodations")
      .delete()
      .eq("accommodation_id", id as any)
      .eq("club_id", clubId as any);

    if (removeError) {
      console.error(`Erro ao remover jogadores do alojamento ${id}:`, removeError);
      throw new Error(`Erro ao remover jogadores do alojamento: ${removeError.message}`);
    }

    console.log(`Removidos ${playersCount} jogadores do alojamento ${id}`);
  }

  // Se for um hotel, excluir os quartos primeiro
  try {
    // Verificar se é um hotel
    const { data: accommodation } = await supabase
      .from("accommodations")
      .select("type")
      .eq("id", id as any)
      .eq("club_id", clubId as any)
      .single();

    if (accommodation && 'type' in accommodation && accommodation.type === "hotel") {
      // Excluir os quartos do hotel
      await supabase
        .from("hotel_rooms")
        .delete()
        .eq("accommodation_id", id as any)
        .eq("club_id", clubId as any);
    }
  } catch (roomError) {
    console.error(`Erro ao excluir quartos do hotel ${id}:`, roomError);
    // Continuar mesmo se houver erro ao excluir os quartos
  }

  // Excluir o alojamento
  const { error } = await supabase
    .from("accommodations")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", id as any);

  if (error) {
    console.error(`Erro ao excluir alojamento ${id}:`, error);
    throw new Error(`Erro ao excluir alojamento: ${error.message}`);
  }

  return { success: true, playersRemoved: playersCount };
}

// Funções para gerenciar associações entre jogadores e alojamentos
export async function getPlayerAccommodations(clubId: number, playerId?: string): Promise<PlayerAccommodation[]> {
  let query = supabase
    .from("player_accommodations")
    .select(`
      *,
      players:player_id (name, nickname, birthdate, entry_date, status),
      accommodations:accommodation_id (name, type),
      hotel_rooms!hotel_room_id (room_number)
    `)
    .eq("club_id", clubId as any)
    .or('status.eq.active,status.is.null')
    .is('check_out_date', null);

  if (playerId) {
    query = query.eq("player_id", playerId as any);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar alojamentos dos jogadores:", error);
    throw new Error(`Erro ao buscar alojamentos dos jogadores: ${error.message}`);
  }

  // Formatar os dados para incluir os nomes dos jogadores, alojamentos e número do quarto
  return (data || []).map(item => {
    // Garantir que item é do tipo esperado com as propriedades necessárias
    const typedItem = item as unknown as PlayerAccommodation & { hotel_rooms?: { room_number: string } };
    return {
      ...(typedItem as object),
      player_name: typedItem.players?.name,
      player_nickname: typedItem.players?.nickname,
      player_birthdate: typedItem.players?.birthdate,
      player_entry_date: typedItem.players?.entry_date,
      accommodation_name: typedItem.accommodations?.name,
      room_number: typedItem.hotel_rooms?.room_number || typedItem.room_number || ''
    };
  }) as PlayerAccommodation[];
}


export async function getAccommodationPlayers(clubId: number, accommodationId: number): Promise<PlayerAccommodation[]> {
  const { data, error } = await supabase
    .from("player_accommodations")
    .select(`
      *,
      players:player_id (name, nickname, birthdate, entry_date, status),
      accommodations:accommodation_id (name, type)
    `)
    .eq("club_id", clubId as any)
    .eq("accommodation_id", accommodationId as any)
    .or('status.eq.active,status.is.null')
    .is('check_out_date', null);

  if (error) {
    console.error(`Erro ao buscar jogadores do alojamento ${accommodationId}:`, error);
    throw new Error(`Erro ao buscar jogadores do alojamento: ${error.message}`);
  }

  // Formatar os dados para incluir os nomes dos jogadores, alojamentos e número do quarto
  return (data || []).map(item => {
    // Garantir que item é do tipo esperado com as propriedades necessárias
    const typedItem = item as unknown as PlayerAccommodation & { hotel_rooms?: { room_number: string } };
    return {
      ...(typedItem as object),
      player_name: typedItem.players?.name,
      player_nickname: typedItem.players?.nickname,
      player_birthdate: typedItem.players?.birthdate,
      player_entry_date: typedItem.players?.entry_date,
      accommodation_name: typedItem.accommodations?.name,
      room_number: typedItem.hotel_rooms?.room_number || typedItem.room_number || ''
    };
  }) as PlayerAccommodation[];
}

export async function assignPlayerToAccommodation(
  clubId: number,
  playerId: string,
  accommodationId: number,
  details: {
    room_number?: string;
    hotel_room_id?: number;
    check_in_date?: string;
    check_out_date?: string;
    status?: string;
    notes?: string;
  }
): Promise<PlayerAccommodation> {
  // Verificar se o jogador já está associado a este alojamento
  const { data: existingAssignments } = await supabase
    .from("player_accommodations")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("player_id", playerId as any)
    .eq("accommodation_id", accommodationId as any)
    .eq("status", "active" as any);

  if (existingAssignments && existingAssignments.length > 0) {
    throw new Error("O jogador já está associado a este alojamento");
  }

  // Buscar informações do alojamento
  const { data: accommodation, error: accommodationError } = await supabase
    .from("accommodations")
    .select("*")
    .eq("id", accommodationId as any)
    .eq("club_id", clubId as any)
    .single();

  if (accommodationError) {
    console.error("Erro ao buscar informações do alojamento:", accommodationError);
    throw new Error(`Erro ao buscar informações do alojamento: ${accommodationError.message}`);
  }

  // Se for um hotel e tiver um quarto específico selecionado
  if ((accommodation as any).type === "hotel" && details.hotel_room_id) {
    // Verificar a capacidade do quarto
    try {
      const { getRoomAvailability } = await import('./hotelRooms');
      const { capacity, occupied } = await getRoomAvailability(clubId as any, details.hotel_room_id as any);

      // Verificar se o quarto está cheio
      if (occupied >= capacity) {
        throw new Error(`Capacidade máxima do quarto atingida (${capacity} jogadores)`);
      }
    } catch (roomError) {
      console.error("Erro ao verificar disponibilidade do quarto:", roomError);
      throw new Error(`Erro ao verificar disponibilidade do quarto: ${roomError.message}`);
    }
  }
  // Se for um alojamento com capacidade geral definida (apartamento ou hotel sem quarto específico)
  else if ((accommodation as any).capacity !== null) {
    // Contar quantos jogadores já estão alojados
    const { data: currentPlayers, error: countError } = await supabase
      .from("player_accommodations")
      .select("id")
      .eq("accommodation_id", accommodationId as any)
      .eq("club_id", clubId as any)
      .eq("status", "active" as any);

    if (countError) {
      console.error("Erro ao contar jogadores no alojamento:", countError);
      throw new Error(`Erro ao verificar capacidade do alojamento: ${countError.message}`);
    }

    // Verificar se a capacidade foi atingida
    if (currentPlayers && (currentPlayers as any[]).length >= (accommodation as any).capacity) {
      throw new Error(`Capacidade máxima do alojamento atingida (${(accommodation as any).capacity} jogadores)`);
    }
  }

  // Criar a associação
  const { data, error } = await supabase
    .from("player_accommodations")
    .insert({
      club_id: clubId,
      player_id: playerId,
      accommodation_id: accommodationId,
      room_number: details.room_number,
      hotel_room_id: details.hotel_room_id,
      check_in_date: details.check_in_date,
      check_out_date: details.check_out_date,
      status: details.status || "active",
      notes: details.notes
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao associar jogador ao alojamento:", error);
    throw new Error(`Erro ao associar jogador ao alojamento: ${error.message}`);
  }

  return data as unknown as PlayerAccommodation;
}

export async function updatePlayerAccommodation(
  clubId: number,
  assignmentId: number,
  details: {
    room_number?: string;
    hotel_room_id?: number;
    check_in_date?: string;
    check_out_date?: string;
    status?: string;
    notes?: string;
  }
): Promise<PlayerAccommodation> {
  // Se estiver mudando para um quarto específico, verificar a capacidade
  if (details.hotel_room_id) {
    try {
      const { getRoomAvailability } = await import('./hotelRooms');
      const { capacity, occupied } = await getRoomAvailability(clubId as any, details.hotel_room_id as any);

      // Verificar se o quarto está cheio
      if (occupied >= capacity) {
        throw new Error(`Capacidade máxima do quarto atingida (${capacity} jogadores)`);
      }
    } catch (roomError) {
      console.error("Erro ao verificar disponibilidade do quarto:", roomError);
      throw new Error(`Erro ao verificar disponibilidade do quarto: ${roomError.message}`);
    }
  }

  const { data, error } = await supabase
    .from("player_accommodations")
    .update(details as any)
    .eq("club_id", clubId as any)
    .eq("id", assignmentId as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar associação ${assignmentId}:`, error);
    throw new Error(`Erro ao atualizar associação: ${error.message}`);
  }

  return data as unknown as PlayerAccommodation;
}

export async function removePlayerFromAccommodation(clubId: number, assignmentId: number): Promise<boolean> {
  const { error } = await supabase
    .from("player_accommodations")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", assignmentId as any);

  if (error) {
    console.error(`Erro ao remover jogador do alojamento:`, error);
    throw new Error(`Erro ao remover jogador do alojamento: ${error.message}`);
  }

  return true;
}















