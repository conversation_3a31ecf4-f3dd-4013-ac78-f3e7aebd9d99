import { useState, useEffect, use<PERSON>emo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  DollarSign,
  Download,
  FileMinus,
  FilePlus,
  MoreHorizontal,
  Plus,
  Shield,
  TrendingDown,
  TrendingUp,
  FileCheck,
  Calendar,
  Search,
  Filter,
  Users,
} from "lucide-react";
import { NovaTransacaoDialog } from "@/components/modals/NovaTransacaoDialog";
import { getSalaries, createPlayerSalary, updatePlayerSalary, deletePlayerSalary, getContracts, deleteContract, getClubInfo, FinancialAccount, getPlayerById } from "@/api";
import { getSalaryAdvances } from "@/api/salaryAdvances";
import { getCollaborators } from "@/api/collaborators";
import { useFinancialStore } from "@/store/useFinancialStore";
import { useFinancialAccountsStore } from "@/store/useFinancialAccountsStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { ContractDialog } from "@/components/modals/ContractDialog";
import { SalaryDialog } from "@/components/modals/SalaryDialog";
import { ReportDialog } from "@/components/modals/ReportDialog";
import { TransactionDetailsDialog } from "@/components/modals/TransactionDetailsDialog";
import { EditTransactionDialog } from "@/components/modals/EditTransactionDialog";
import { ReceiptDialog } from "@/components/modals/ReceiptDialog";
import { BonusDialog } from "@/components/modals/BonusDialog";
import { DeleteTransactionDialog } from "@/components/modals/DeleteTransactionDialog";
import { ContasDialog } from "@/components/modals/ContasDialog";
import { UploadReceiptDialog } from "@/components/modals/UploadReceiptDialog";
import { AccountDetailsDialog } from "@/components/modals/AccountDetailsDialog";
import { AccountsReportDialog } from "@/components/modals/AccountsReportDialog";
import { AccountsPayableGeneralReportDialog } from "@/components/modals/AccountsPayableGeneralReportDialog";
import { CashFlowReportDialog } from "@/components/modals/CashFlowReportDialog";
import { AccountsPayablePeriodReportDialog } from "@/components/modals/AccountsPayablePeriodReportDialog";
import { AdicionarValeDialog } from "@/components/financeiro/AdicionarValeDialog";
import { ColaboradorFinanceiroDialog } from "@/components/administrativo/ColaboradorFinanceiroDialog";
import { toast } from "@/hooks/use-toast";
// Importar jsPDF e jspdf-autotable
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';
// Importar date-fns para formatação de datas
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";

// Definir interface para o jsPDF estendido com autoTable
interface jsPDFWithAutoTable extends Omit<jsPDF, 'internal'> {
  autoTable: typeof autoTable;
  lastAutoTable?: any; // Adicionado para resolver o erro de tipagem
  internal: {
    getNumberOfPages: () => number;
    pageSize: {
      width: number;
      height: number;
      getWidth: () => number;
      getHeight: () => number;
    };
    scaleFactor: number;
    pages: number[];
    events: any;
    getEncryptor: (objectId: number) => (data: string) => string;
  };
}

// Estender o tipo FinancialTransaction para incluir player_id e player_name
declare module "@/store/useFinancialStore" {
  interface FinancialTransaction {
    player_id?: string;
    player_name?: string;
  }
}

// Estender o tipo FinancialTransaction para incluir player_id, player_name e collaborator_id
interface ExtendedFinancialTransaction {
  id: number;
  club_id: number;
  date: string;
  type: string;
  category: string;
  amount: number;
  description: string;
  player_id?: string;
  player_name?: string;
  collaborator_id?: number;
}

// Função para formatar a data sem problemas de fuso horário
function formatDateWithoutTimezone(dateString: string): string {
  if (!dateString) return '-';

  // Dividir a string de data no formato YYYY-MM-DD
  const parts = dateString.split('-');
  if (parts.length !== 3) return dateString;

  // Criar uma data no formato brasileiro (DD/MM/YYYY)
  return `${parts[2].padStart(2, '0')}/${parts[1].padStart(2, '0')}/${parts[0]}`;
}

export default function Financeiro() {
  const [activeTab, setActiveTab] = useState("fluxo");
  const [novaTransacaoDialogOpen, setNovaTransacaoDialogOpen] = useState(false);
  const [contractDialogOpen, setContractDialogOpen] = useState(false);
  const [salaryDialogOpen, setSalaryDialogOpen] = useState(false);
  const [reportDialogOpen, setReportDialogOpen] = useState(false);
  const [editingSalary, setEditingSalary] = useState(null);
  const [editingContract, setEditingContract] = useState(null);
  const [salaries, setSalaries] = useState([]);
  const [collaborators, setCollaborators] = useState([]);
  const [contracts, setContracts] = useState([]);
  const { transactions: financialTransactions, fetchTransactions, markAsPaid } = useFinancialStore();
  const { accounts, fetchAccounts, markAsPaid: markAccountAsPaid, deleteAccount } = useFinancialAccountsStore();
  const clubId = useCurrentClubId();

  // Estados para os novos modais
  const [transactionDetailsOpen, setTransactionDetailsOpen] = useState(false);
  const [editTransactionOpen, setEditTransactionOpen] = useState(false);
  const [receiptDialogOpen, setReceiptDialogOpen] = useState(false);
  const [bonusDialogOpen, setBonusDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<ExtendedFinancialTransaction | null>(null);

  // Estados para os modais de contas a pagar/receber
  const [contasDialogOpen, setContasDialogOpen] = useState(false);
  const [uploadReceiptDialogOpen, setUploadReceiptDialogOpen] = useState(false);
  const [accountDetailsDialogOpen, setAccountDetailsDialogOpen] = useState(false);
  const [accountsReportDialogOpen, setAccountsReportDialogOpen] = useState(false);
  const [accountsPayableGeneralReportDialogOpen, setAccountsPayableGeneralReportDialogOpen] = useState(false);
  const [cashFlowReportDialogOpen, setCashFlowReportDialogOpen] = useState(false);
  const [accountsPayablePeriodReportDialogOpen, setAccountsPayablePeriodReportDialogOpen] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<FinancialAccount | null>(null);

  // Estados para o diálogo de adiantamento (vale)
  const [adicionarValeDialogOpen, setAdicionarValeDialogOpen] = useState(false);
  const [selectedSalary, setSelectedSalary] = useState<any>(null);
  const [salaryAdvances, setSalaryAdvances] = useState<any[]>([]);

  // Estados para o diálogo de finanças do colaborador
  const [collaboratorFinanceDialogOpen, setCollaboratorFinanceDialogOpen] = useState(false);
  const [selectedCollaborator, setSelectedCollaborator] = useState<any>(null);

  // Estados para filtros de contas
  const [accountTypeFilter, setAccountTypeFilter] = useState("todos");
  const [accountStatusFilter, setAccountStatusFilter] = useState("todos");
  const [accountDescriptionFilter, setAccountDescriptionFilter] = useState("");
  const [paymentSourceFilter, setPaymentSourceFilter] = useState("todos");

  // Estado para filtros
  const [descriptionFilter, setDescriptionFilter] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [paymentStatusFilter, setPaymentStatusFilter] = useState("todos"); // Novo filtro para status de pagamento

  // --- Estados para filtros ---
  const now = new Date();
  const [selectedMonth, setSelectedMonth] = useState(now.getMonth());
  const [selectedYear, setSelectedYear] = useState(now.getFullYear());

  // Transações filtradas
  const filteredTransactions = financialTransactions
    .filter(t => {
      // Filtro por descrição
      if (descriptionFilter && !t.description.toLowerCase().includes(descriptionFilter.toLowerCase())) {
        return false;
      }

      // Filtro por categoria
      if (categoryFilter && t.category !== categoryFilter) {
        return false;
      }

      // Filtro por status de pagamento
      if (paymentStatusFilter !== "todos") {
        if (paymentStatusFilter === "pagos" && t.payment_status !== "paid") {
          return false;
        }
        if (paymentStatusFilter === "pendentes" && t.payment_status !== "pending") {
          return false;
        }
        if (paymentStatusFilter === "pagos_despesas" && (t.type !== "despesa" || t.payment_status !== "paid")) {
          return false;
        }
      }

      // Filtro por mês e ano
      try {
        // Extrair mês diretamente da string de data (formato YYYY-MM-DD)
        const dateParts = t.date.split('-');
        if (dateParts.length === 3) {
          const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
          const transactionYear = parseInt(dateParts[0]);

          if (transactionMonth !== selectedMonth || transactionYear !== selectedYear) {
            return false;
          }
        }
      } catch (err) {
        console.error(`Erro ao processar data da transação: ${t.date}`, err);
      }

      return true;
    }) as ExtendedFinancialTransaction[];

  // Meses para o seletor
  const months = [
    "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
    "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
  ];

  useEffect(() => {
    async function fetchData() {
      const [sal, col, con, advances] = await Promise.all([
        getSalaries(clubId),
        getCollaborators(clubId),
        getContracts(clubId),
        getSalaryAdvances(clubId, selectedMonth + 1, selectedYear)
      ]);
      setSalaries(sal);
      setCollaborators(col);
      setContracts(con);
      setSalaryAdvances(advances);
      fetchTransactions(clubId);
      fetchAccounts(clubId);
    }
    fetchData();
  }, [fetchTransactions, fetchAccounts, clubId, selectedMonth, selectedYear]);

  // Receitas e despesas do mês selecionado
  const receitasMes = financialTransactions.filter(t => {
    try {
      if (t.type !== "receita") return false;

      // Aplicar filtro de mês/ano
      // Extrair mês diretamente da string de data (formato YYYY-MM-DD)
      const dateParts = t.date.split('-');
      const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
      const transactionYear = parseInt(dateParts[0]);

      return transactionMonth === selectedMonth &&
        transactionYear === selectedYear;
    } catch (err) {
      console.error(`Erro ao processar data da transação: ${t.date}`, err);
      return false;
    }
  });

  const despesasMes = financialTransactions.filter(t => {
    try {
      if (t.type !== "despesa") return false;

      // Somente incluir despesas que foram pagas
      if (t.payment_status !== 'paid') return false;

      // Aplicar filtro de mês/ano
      // Extrair mês diretamente da string de data (formato YYYY-MM-DD)
      const dateParts = t.date.split('-');
      const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
      const transactionYear = parseInt(dateParts[0]);

      return transactionMonth === selectedMonth &&
        transactionYear === selectedYear;
    } catch (err) {
      console.error(`Erro ao processar data da transação: ${t.date}`, err);
      return false;
    }
  });

  const totalReceitas = receitasMes.reduce((acc, t) => acc + (typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount), 0);
  const totalDespesas = despesasMes.reduce((acc, t) => acc + (typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount), 0);

  // Calcular variação percentual em relação ao mês anterior
  const mesAnterior = selectedMonth === 0 ? 11 : selectedMonth - 1;
  const anoAnterior = selectedMonth === 0 ? selectedYear - 1 : selectedYear;

  const receitasMesAnterior = financialTransactions.filter(t => {
    try {
      if (t.type !== "receita") return false;

      // Extrair mês diretamente da string de data (formato YYYY-MM-DD)
      const dateParts = t.date.split('-');
      const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
      const transactionYear = parseInt(dateParts[0]);

      return transactionMonth === mesAnterior &&
        transactionYear === anoAnterior;
    } catch (err) {
      return false;
    }
  });

  const despesasMesAnterior = financialTransactions.filter(t => {
    try {
      if (t.type !== "despesa") return false;

      // Somente incluir despesas que foram pagas
      if (t.payment_status !== 'paid') return false;

      // Extrair mês diretamente da string de data (formato YYYY-MM-DD)
      const dateParts = t.date.split('-');
      const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
      const transactionYear = parseInt(dateParts[0]);

      return transactionMonth === mesAnterior &&
        transactionYear === anoAnterior;
    } catch (err) {
      return false;
    }
  });

  const totalReceitasAnterior = receitasMesAnterior.reduce((acc, t) =>
    acc + (typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount), 0
  );

  const totalDespesasAnterior = despesasMesAnterior.reduce((acc, t) =>
    acc + (typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount), 0
  );

  // Calcular percentuais de variação
  const variacaoReceitas = totalReceitasAnterior > 0
    ? Math.round(((totalReceitas - totalReceitasAnterior) / totalReceitasAnterior) * 100)
    : (totalReceitas > 0 ? 100 : 0);

  const variacaoDespesas = totalDespesasAnterior > 0
    ? Math.round(((totalDespesas - totalDespesasAnterior) / totalDespesasAnterior) * 100)
    : (totalDespesas > 0 ? 100 : 0);

  // Folha de pagamento do mês (soma dos salários ativos e transações de salários)
  console.log("Salários de jogadores:", salaries);

  // Calcular salários dos jogadores (valor bruto)
  const salarioJogadores = salaries.reduce((acc, s) => {
    console.log(`Processando salário de jogador: ${JSON.stringify(s)}`);
    // Verificar qual campo contém o valor do salário (value é o campo correto na tabela player_salaries)
    const salaryValue = s.value || 0;
    console.log(`Valor do salário: ${salaryValue}, tipo: ${typeof salaryValue}`);
    const numericValue = typeof salaryValue === 'string' ? parseFloat(salaryValue) : salaryValue;
    console.log(`Valor numérico: ${numericValue}`);
    return acc + numericValue;
  }, 0);

  // Filtrar transações financeiras da categoria "salários" para o mês atual
  const transacoesSalarios = financialTransactions.filter(t => {
    try {
      if (t.category !== "salários" || t.type !== "despesa") return false;

      // Somente incluir despesas que foram pagas
      if (t.payment_status !== 'paid') return false;

      // Extrair mês diretamente da string de data (formato YYYY-MM-DD)
      const dateParts = t.date.split('-');
      const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
      const transactionYear = parseInt(dateParts[0]);

      return transactionMonth === selectedMonth && transactionYear === selectedYear;
    } catch (err) {
      return false;
    }
  });

  console.log("Transações de salários do mês:", transacoesSalarios);

  // Calcular salários dos colaboradores a partir das transações
  const salarioColaboradores = transacoesSalarios
    .filter(t => t.collaborator_id) // Filtrar apenas transações com collaborator_id
    .reduce((acc, t) => acc + (typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount), 0);

  console.log(`Salário total de jogadores: ${salarioJogadores}`);
  console.log(`Salário total de colaboradores: ${salarioColaboradores}`);

  // Calcular vales (adiantamentos) do mês atual para jogadores e colaboradores
  const valesJogadores = useMemo(() => {
    return salaryAdvances
      .filter(advance =>
        advance.person_type === 'player' &&
        advance.month === selectedMonth + 1 &&
        advance.year === selectedYear &&
        advance.status === 'active'
      )
      .reduce((total, advance) => total + advance.amount, 0);
  }, [salaryAdvances, selectedMonth, selectedYear]);

  const valesColaboradores = useMemo(() => {
    return salaryAdvances
      .filter(advance =>
        advance.person_type === 'collaborator' &&
        advance.month === selectedMonth + 1 &&
        advance.year === selectedYear &&
        advance.status === 'active'
      )
      .reduce((total, advance) => total + advance.amount, 0);
  }, [salaryAdvances, selectedMonth, selectedYear]);

  // Calcular salários líquidos (bruto - vales)
  const salarioLiquidoJogadores = salarioJogadores - valesJogadores;
  const salarioLiquidoColaboradores = salarioColaboradores - valesColaboradores;

  // Somar os salários líquidos de jogadores e colaboradores para a folha de pagamento
  const folhaPagamento = salarioLiquidoJogadores + salarioLiquidoColaboradores;

  // Percentual da folha nas despesas
  const folhaPercent = totalDespesas > 0 ? Math.round((folhaPagamento / totalDespesas) * 100) : 0;

  // Contratos ativos
  const contratosAtivos = contracts.filter(c => c.status === "Ativo");

  // Contratos a vencer em breve (próximos 60 dias)
  const agora = new Date();
  const emBreve = contracts.filter(c => {
    if (!c.endDate) return false;
    const end = new Date(c.endDate);
    return c.status === "Ativo" && (end > agora && end < new Date(agora.getTime() + 60 * 24 * 60 * 60 * 1000));
  });

  // Filtrar contas a pagar/receber
  const filteredAccounts = accounts.filter(account => {
    // Filtro por tipo
    if (accountTypeFilter !== "todos" && account.type !== accountTypeFilter) {
      return false;
    }

    // Filtro por status
    if (accountStatusFilter !== "todos" && account.status !== accountStatusFilter) {
      return false;
    }

    // Filtro por descrição
    if (accountDescriptionFilter && !account.description.toLowerCase().includes(accountDescriptionFilter.toLowerCase()) &&
        !account.supplier_client.toLowerCase().includes(accountDescriptionFilter.toLowerCase())) {
      return false;
    }

    return true;
  });

  // Calcular totais para contas a pagar/receber (incluindo todas as contas, não apenas pendentes)
  const totalAPagar = filteredAccounts
    .filter(a => a.type === "a_pagar")
    .reduce((sum, a) => sum + a.amount, 0);

  const totalAReceber = filteredAccounts
    .filter(a => a.type === "a_receber")
    .reduce((sum, a) => sum + a.amount, 0);

  // Calcular total de TODOS os pagamentos pendentes do clube para o mês/ano selecionado - para o card "Contas a Pagar"
  const totalTodosPagamentos = useMemo(() => {
    let total = 0;
    const currentMonth = selectedMonth;
    const currentYear = selectedYear;

    // 1. Somar contas a pagar pendentes do mês/ano selecionado
    const contasPendentes = accounts.filter(a => {
      if (a.type !== "a_pagar" || a.status !== "pendente") return false;
      
      try {
        const dueDate = new Date(a.due_date);
        return dueDate.getMonth() === currentMonth && dueDate.getFullYear() === currentYear;
      } catch (e) {
        console.error("Erro ao processar data da conta:", a, e);
        return false;
      }
    });

    contasPendentes.forEach(a => {
      const valor = typeof a.amount === 'string' ? parseFloat(a.amount) : a.amount;
      total += valor;
    });

    // 2. Somar TODAS as transações de despesa pendentes do mês/ano selecionado
    const transacoesPendentes = financialTransactions.filter(t => {
      // Verifica se é uma despesa e está pendente (tanto 'pendente' quanto 'pending')
      if (t.type !== "despesa" || (t.payment_status !== "pendente" && t.payment_status !== "pending")) {
        return false;
      }
      
      try {
        const transactionDate = new Date(t.date);
        return transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear;
      } catch (e) {
        console.error("Erro ao processar data da transação:", t, e);
        return false;
      }
    });

    transacoesPendentes.forEach(t => {
      const valor = typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount;
      total += valor;
    });

    return total;
  }, [accounts, financialTransactions, selectedMonth, selectedYear]);

  // Calcular contas a pagar filtradas por mês/ano selecionado - para o card "Contas a Pagar"
  const contasAPagarMes = useMemo(() => {
    let total = 0;

    // Filtrar contas a pagar pelo mês/ano do due_date
    const contasFiltradas = accounts.filter(a => {
      if (a.type !== "a_pagar") return false;

      try {
        const dueDate = new Date(a.due_date);
        const dueDateMonth = dueDate.getMonth(); // 0-11
        const dueDateYear = dueDate.getFullYear();

        return dueDateMonth === selectedMonth && dueDateYear === selectedYear;
      } catch (err) {
        console.error(`Erro ao processar data de vencimento: ${a.due_date}`, err);
        return false;
      }
    });

    // Somar o valor das contas filtradas
    total += contasFiltradas.reduce((sum, a) => sum + (typeof a.amount === 'string' ? parseFloat(a.amount) : a.amount), 0);

    // Somar transações de despesa do mês que não são originadas de contas
    total += financialTransactions
      .filter(t => {
        if (t.type !== "despesa") return false;

        // Verificar se não é uma transação criada automaticamente por uma conta
        const isFromAccount = accounts.some(account =>
          t.description === `${account.description} - ${account.supplier_client}`
        );

        if (isFromAccount) return false;

        // Aplicar filtro de mês/ano
        try {
          const dateParts = t.date.split('-');
          const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
          const transactionYear = parseInt(dateParts[0]);

          return transactionMonth === selectedMonth && transactionYear === selectedYear;
        } catch (err) {
          return false;
        }
      })
      .reduce((sum, t) => sum + (typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount), 0);

    return total;
  }, [accounts, financialTransactions, selectedMonth, selectedYear]);

  // Contar quantos pagamentos pendentes existem no mês selecionado - para o card "Contas a Pagar"
  const totalPagamentosPendentesMes = useMemo(() => {
    let count = 0;

    // Contar contas a pagar pendentes do mês selecionado
    count += accounts.filter(a => {
      if (a.type !== "a_pagar" || a.status !== "pendente") return false;

      try {
        const dueDate = new Date(a.due_date);
        const dueDateMonth = dueDate.getMonth(); // 0-11
        const dueDateYear = dueDate.getFullYear();

        return dueDateMonth === selectedMonth && dueDateYear === selectedYear;
      } catch (err) {
        return false;
      }
    }).length;

    // Contar transações de despesa pendentes do mês (que não são de contas)
    count += financialTransactions
      .filter(t => {
        if (t.type !== "despesa" || (t.payment_status !== "pending" && t.payment_status !== "pendente")) return false;

        // Verificar se não é uma transação criada automaticamente por uma conta
        const isFromAccount = accounts.some(account =>
          t.description === `${account.description} - ${account.supplier_client}`
        );
        if (isFromAccount) return false;

        // Aplicar filtro de mês/ano
        try {
          const dateParts = t.date.split('-');
          const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
          const transactionYear = parseInt(dateParts[0]);

          return transactionMonth === selectedMonth && transactionYear === selectedYear;
        } catch (err) {
          return false;
        }
      }).length;

    return count;
  }, [accounts, financialTransactions, selectedMonth, selectedYear]);

  // Contar quantos pagamentos pendentes existem no total (sem filtro de mês) - para o card "Contratos Ativos"
  const totalPagamentosPendentes = useMemo(() => {
    let count = 0;

    // Contar contas a pagar pendentes
    count += accounts.filter(a => a.type === "a_pagar" && a.status === "pendente").length;

    // Contar transações de despesa pendentes (que não são de contas)
    count += financialTransactions
      .filter(t => t.type === "despesa" && (t.payment_status === "pending" || t.payment_status === "pendente"))
      .filter(t => {
        // Verificar se não é uma transação criada automaticamente por uma conta
        // Transações automáticas têm formato: "descrição_conta - fornecedor_conta"
        const isFromAccount = accounts.some(account =>
          t.description === `${account.description} - ${account.supplier_client}`
        );
        return !isFromAccount;
      }).length;

    return count;
  }, [accounts, financialTransactions]);

  // Criar lista unificada de todos os pagamentos (contas + transações pagas)
  const allPayments = useMemo(() => {
    const payments = [];

    // Adicionar todas as contas a pagar (pendentes e pagas)
    accounts.filter(a => a.type === "a_pagar").forEach(account => {
      payments.push({
        id: `account_${account.id}`,
        type: 'account',
        description: account.description,
        supplier_client: account.supplier_client,
        amount: account.amount,
        date: account.due_date,
        status: account.status,
        category: account.category,
        source: 'Conta a Pagar',
        original: account
      });
    });

    // Adicionar TODAS as transações de despesa (pagas e pendentes) que não são originadas de contas
    financialTransactions
      .filter(t => t.type === "despesa")
      .forEach(transaction => {
        // Verificar se não é uma transação criada automaticamente por uma conta
        // Transações automáticas têm formato: "descrição_conta - fornecedor_conta"
        const isFromAccount = accounts.some(account =>
          transaction.description === `${account.description} - ${account.supplier_client}`
        );

        if (!isFromAccount) {
          payments.push({
            id: `transaction_${transaction.id}`,
            type: 'transaction',
            description: transaction.description,
            supplier_client: transaction.player_name || transaction.collaborator_role || 'Direto',
            amount: transaction.amount,
            date: transaction.date,
            status: transaction.payment_status === 'paid' ? 'pago' : 'pendente',
            category: transaction.category,
            source: 'Transação Direta',
            original: transaction
          });
        }
      });

    return payments.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [accounts, financialTransactions]);

  // Filtrar pagamentos unificados
  const filteredPayments = allPayments.filter(payment => {
    // Filtro por descrição/fornecedor
    if (accountDescriptionFilter &&
        !payment.description.toLowerCase().includes(accountDescriptionFilter.toLowerCase()) &&
        !payment.supplier_client.toLowerCase().includes(accountDescriptionFilter.toLowerCase())) {
      return false;
    }

    // Filtro por status
    if (accountStatusFilter !== "todos") {
      if (accountStatusFilter !== payment.status) {
        return false;
      }
    }

    // Filtro por origem
    if (paymentSourceFilter !== "todos") {
      if (paymentSourceFilter !== payment.type) {
        return false;
      }
    }

    return true;
  });

  // Log para depuração
  useEffect(() => {
    console.log("Data atual:", new Date());
    console.log("Mês selecionado:", selectedMonth);
    console.log("Ano selecionado:", selectedYear);
    console.log("Todas as transações:", financialTransactions);
    console.log("Receitas do mês:", receitasMes);
    console.log("Despesas do mês:", despesasMes);
    console.log("Total receitas:", totalReceitas);
    console.log("Total despesas:", totalDespesas);

    console.log("Data das transações:");
    financialTransactions.forEach(t => {
      const dateParts = t.date.split('-');
      const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
      const transactionYear = parseInt(dateParts[0]);
      console.log(`ID: ${t.id}, Data: ${t.date}, Tipo: ${t.type}, Valor: ${t.amount}, Mês: ${transactionMonth}, Ano: ${transactionYear}`);
    });
  }, [financialTransactions, selectedMonth, selectedYear, receitasMes, despesasMes, totalReceitas, totalDespesas]);

  // Função para abrir o diálogo de relatório
  const handleOpenReportDialog = () => {
    setReportDialogOpen(true);
  };

  // Função para formatar data sem timezone
  const formatDateWithoutTimezone = (dateString: string) => {
    try {
      // Use parseISO to avoid timezone issues with date strings in YYYY-MM-DD format
      const date = parseISO(dateString);
      return format(date, "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      console.error("Erro ao formatar data:", error);
      return dateString;
    }
  };

  // Função para gerar relatório de contas a pagar
  const generateAccountsPayableReport = async (type: "accounts_payable" | "accounts_payable_period", month?: number, year?: number) => {
    // Buscar informações do clube
    const clubInfo = await getClubInfo(clubId);

    // Filtrar contas a pagar
    let filteredAccounts = accounts.filter(a => a.type === "a_pagar" && a.status === "pendente");

    // Se for por período, filtrar por mês/ano
    if (type === "accounts_payable_period" && month !== undefined && year !== undefined) {
      filteredAccounts = filteredAccounts.filter(account => {
        const dueDate = new Date(account.due_date);
        return dueDate.getMonth() === month && dueDate.getFullYear() === year;
      });
    }

    // Incluir transações financeiras pendentes
    const pendingTransactions = [];

    // Filtrar transações financeiras pendentes (despesas não pagas)
    let filteredTransactions = financialTransactions.filter(t =>
      t.type === "despesa" && t.payment_status === "pending"
    );

    // Se for por período, filtrar por mês/ano
    if (type === "accounts_payable_period" && month !== undefined && year !== undefined) {
      filteredTransactions = filteredTransactions.filter(transaction => {
        const transactionDate = new Date(transaction.date);
        return transactionDate.getMonth() === month && transactionDate.getFullYear() === year;
      });
    }

    // Converter transações financeiras para formato de contas a pagar
    for (const transaction of filteredTransactions) {
      let supplierClient = "Não informado";
      let description = transaction.description;

      // Tentar obter informações mais detalhadas
      if (transaction.player_id) {
        try {
          const player = await getPlayerById(clubId, transaction.player_id);
          if (player.status !== "inativo") {
            supplierClient = player.name;
            description = `${transaction.description} - ${player.name}`;
          }
        } catch (error) {
          console.error("Erro ao buscar jogador:", error);
        }
      } else if (transaction.collaborator_id) {
        try {
          const { data: collaboratorData, error } = await supabase
            .from("collaborators")
            .select("full_name")
            .eq("id", transaction.collaborator_id)
            .eq("club_id", clubId)
            .single();

          if (!error && collaboratorData) {
            supplierClient = collaboratorData.full_name;
            description = `${transaction.description} - ${collaboratorData.full_name}`;
          }
        } catch (error) {
          console.error("Erro ao buscar colaborador:", error);
        }
      }

      pendingTransactions.push({
        description: description,
        supplier_client: supplierClient,
        due_date: transaction.date,
        amount: typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount,
        status: "pendente",
        category: transaction.category || "Geral",
        type: "transaction"
      });
    }

    // Incluir transações de salários pendentes (não pagas) com desconto de vales
    const salaryAccounts = [];

    // Buscar transações de salários pendentes do período
    const salaryTransactions = financialTransactions.filter(t => {
      if (t.category !== "salários" || t.type !== "despesa" || t.payment_status === 'paid') return false;

      // Se for por período, filtrar por mês/ano
      if (type === "accounts_payable_period" && month !== undefined && year !== undefined) {
        try {
          const dateParts = t.date.split('-');
          const transactionMonth = parseInt(dateParts[1]) - 1;
          const transactionYear = parseInt(dateParts[0]);
          return transactionMonth === month && transactionYear === year;
        } catch (err) {
          return false;
        }
      }

      return true;
    });

    // Processar cada transação de salário
    for (const transaction of salaryTransactions) {
      try {
        let personName = '';
        let personType = '';
        let personId = null;

        // Identificar se é jogador ou colaborador
        if (transaction.player_id) {
          personType = 'player';
          personId = transaction.player_id;
          // Extrair nome da descrição (formato: "Salário - Nome")
          const descriptionParts = transaction.description.split(' - ');
          if (descriptionParts.length > 1) {
            // Se a descrição tem o formato "Salário - Nome", usar apenas o nome
            personName = descriptionParts.slice(1).join(' - '); // Caso tenha mais de um " - " no nome
          } else {
            // Se não tem o formato esperado, usar a descrição completa ou fallback
            personName = transaction.description.includes('Salário') ?
              transaction.description.replace('Salário', '').trim() :
              `Jogador ${transaction.player_id}`;
          }
        } else if (transaction.collaborator_id) {
          personType = 'collaborator';
          personId = transaction.collaborator_id;
          // Extrair nome da descrição (formato: "Salário - Nome")
          const descriptionParts = transaction.description.split(' - ');
          if (descriptionParts.length > 1) {
            // Se a descrição tem o formato "Salário - Nome", usar apenas o nome
            personName = descriptionParts.slice(1).join(' - '); // Caso tenha mais de um " - " no nome
          } else {
            // Se não tem o formato esperado, usar a descrição completa ou fallback
            personName = transaction.description.includes('Salário') ?
              transaction.description.replace('Salário', '').trim() :
              `Colaborador ${transaction.collaborator_id}`;
          }
        }

        if (personId && personType) {
          // Buscar vales para esta pessoa no mês da transação
          const dateParts = transaction.date.split('-');
          const transactionMonth = parseInt(dateParts[1]);
          const transactionYear = parseInt(dateParts[0]);

          const vales = await getSalaryAdvances(clubId, transactionMonth, transactionYear, personType as 'player' | 'collaborator', personId);
          const totalVales = vales.reduce((total, vale) => total + vale.amount, 0);

          // Calcular salário líquido (salário - vales)
          const salarioBase = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;
          const salarioLiquido = salarioBase - totalVales;

          // Buscar informações de PIX
          let pixKey = 'Não informado';

          try {
            if (personType === 'player') {
              // Buscar PIX do jogador
              const { data: playerData } = await supabase
                .from("players")
                .select("bank_info")
                .eq("id", personId)
                .eq("club_id", clubId)
                .single();

              if (playerData?.bank_info?.pix) {
                pixKey = playerData.bank_info.pix;
              }
            } else if (personType === 'collaborator') {
              // Buscar PIX do colaborador
              const { data: collaboratorData } = await supabase
                .from("collaborators")
                .select("bank_info")
                .eq("id", personId)
                .eq("club_id", clubId)
                .single();

              if (collaboratorData?.bank_info?.pix) {
                pixKey = collaboratorData.bank_info.pix;
              }
            }
          } catch (error) {
            console.error("Erro ao buscar PIX:", error);
          }

          // Só incluir se ainda há valor a pagar (salário líquido > 0)
          if (salarioLiquido > 0) {
            salaryAccounts.push({
              description: `Salário Líquido - ${personName}`,
              supplier_client: personName,
              pix_key: pixKey,
              due_date: transaction.date,
              amount: salarioLiquido,
              status: "pendente",
              category: "Salários",
              type: "salary_net"
            });
          }

          // Adicionar vales como itens separados (já pagos/adiantados)
          for (const vale of vales) {
            salaryAccounts.push({
              description: `Vale (Adiantamento) - ${personName}`,
              supplier_client: personName,
              pix_key: pixKey,
              due_date: vale.advance_date,
              amount: vale.amount,
              status: "pago", // Vales são considerados já pagos
              category: "Vales",
              type: "advance"
            });
          }
        }
      } catch (error) {
        console.error("Erro ao processar transação de salário:", error);
      }
    }

    // Não adicionar salários de colaboradores aqui, pois já estão sendo processados nas transações financeiras acima

    // Combinar todas as contas a pagar
    const allPayables = [...filteredAccounts, ...pendingTransactions, ...salaryAccounts];

    // Ordenar por pessoa (agrupar todos os pagamentos de cada pessoa)
    allPayables.sort((a, b) => {
      const nameA = (a.supplier_client || '').toLowerCase();
      const nameB = (b.supplier_client || '').toLowerCase();

      // Primeiro ordenar por nome da pessoa
      const nameComparison = nameA.localeCompare(nameB);
      if (nameComparison !== 0) {
        return nameComparison;
      }

      // Se for a mesma pessoa, ordenar por tipo (salário antes de vale)
      const typeA = a.type || '';
      const typeB = b.type || '';

      // Prioridade: salary_net (salário) antes de advance (vale)
      if (typeA === 'salary_net' && typeB === 'advance') return -1;
      if (typeA === 'advance' && typeB === 'salary_net') return 1;

      return 0;
    });

    // Gerar PDF
    const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

    // Título
    const title = type === "accounts_payable"
      ? "Relatório de Contas a Pagar - Geral"
      : `Relatório de Contas a Pagar - ${months[month || selectedMonth]} ${year || selectedYear}`;

    doc.setFontSize(18);
    doc.text(title, 14, 22);

    // Informações do clube
    doc.setFontSize(12);
    doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);
    doc.text(`Data: ${new Date().toLocaleDateString('pt-BR')}`, 14, 36);

    // Resumo
    const totalAmount = allPayables.reduce((sum, account) => sum + account.amount, 0);
    doc.setFontSize(14);
    doc.text("Resumo", 14, 50);
    doc.setFontSize(10);
    doc.text(`Total de contas: ${allPayables.length}`, 14, 58);
    doc.text(`Valor total: R$ ${totalAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 14, 64);

    // Preparar dados da tabela
    const tableData = allPayables.map(account => [
      account.description,
      account.pix_key || 'Não informado',
      formatDateWithoutTimezone(account.due_date),
      `R$ ${account.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
      account.category || "Geral",
      account.status === "pendente" ? "Pendente" : "Pago"
    ]);

    // Adicionar tabela
    autoTable(doc, {
      startY: 70,
      head: [["Descrição", "Chave PIX", "Vencimento", "Valor", "Categoria", "Status"]],
      body: tableData,
      theme: 'striped',
      headStyles: { fillColor: [0, 87, 183], textColor: [255, 255, 255] },
      styles: {
        fontSize: 8,
        cellPadding: 2,
      },
      columnStyles: {
        3: { halign: 'right' }, // Valor
      }
    });

    // Salvar PDF
    const fileName = type === "accounts_payable"
      ? "relatorio-contas-a-pagar-geral.pdf"
      : `relatorio-contas-a-pagar-${months[month || selectedMonth].toLowerCase()}-${year || selectedYear}.pdf`;

    doc.save(fileName);

    // Mostrar mensagem de sucesso
    toast({
      title: "Relatório gerado com sucesso",
      description: "O relatório de contas a pagar foi baixado para o seu computador.",
    });
  };

  // Função para gerar o relatório financeiro em PDF
  const handleGenerateReport = async (type: "month" | "all" | "payroll" | "department" | "accounts_payable" | "accounts_payable_period", month?: number, year?: number) => {
    try {
      // Buscar informações do clube
      const clubInfo = await getClubInfo(clubId);

      // Definir o título com base no tipo de relatório
      const reportTitle = type === "month"
        ? `Relatório Financeiro - ${months[month || selectedMonth]} ${year || selectedYear}`
        : type === "payroll"
        ? `Relatório de Folha de Pagamento - ${months[month || selectedMonth]} ${year || selectedYear}`
        : type === "department"
        ? `Relatório Financeiro por Departamento - ${months[month || selectedMonth]} ${year || selectedYear}`
        : type === "accounts_payable"
        ? "Relatório de Contas a Pagar - Geral"
        : type === "accounts_payable_period"
        ? `Relatório de Contas a Pagar - ${months[month || selectedMonth]} ${year || selectedYear}`
        : "Relatório Financeiro Completo";

      // Se for relatório por departamento, usar a nova API
      if (type === "department") {
        try {
          // Importar a função de geração de relatório por departamento
          const { generateAndDownloadFinancialReport } = await import("@/api/financialReport");

          // Gerar o relatório
          await generateAndDownloadFinancialReport(
            clubId,
            month || selectedMonth,
            year || selectedYear
          );

          // Mostrar mensagem de sucesso
          toast({
            title: "Relatório gerado com sucesso",
            description: "O relatório financeiro por departamento foi baixado para o seu computador.",
          });

          return; // Encerrar a função aqui
        } catch (error) {
          console.error("Erro ao gerar relatório por departamento:", error);
          toast({
            title: "Erro ao gerar relatório",
            description: "Ocorreu um erro ao gerar o relatório financeiro por departamento.",
            variant: "destructive",
          });
          return;
        }
      }

      // Se for relatório de contas a pagar, usar lógica específica
      if (type === "accounts_payable" || type === "accounts_payable_period") {
        try {
          await generateAccountsPayableReport(type, month, year);
          return; // Encerrar a função aqui
        } catch (error) {
          console.error("Erro ao gerar relatório de contas a pagar:", error);
          toast({
            title: "Erro ao gerar relatório",
            description: "Ocorreu um erro ao gerar o relatório de contas a pagar.",
            variant: "destructive",
          });
          return;
        }
      }

      // Filtrar transações com base no tipo de relatório
      let reportTransactions = [...financialTransactions];
      let reportReceitas = 0;
      let reportDespesas = 0;

      if ((type === "month" || type === "payroll") && month !== undefined && year !== undefined) {
        // Filtrar por mês e ano específicos
        reportTransactions = financialTransactions.filter(t => {
          try {
            // Extrair mês diretamente da string de data (formato YYYY-MM-DD)
            const dateParts = t.date.split('-');
            const transactionMonth = parseInt(dateParts[1]) - 1; // Converter para índice 0-11
            const transactionYear = parseInt(dateParts[0]);

            // Para relatório de folha de pagamento, filtrar apenas transações da categoria "salários"
            if (type === "payroll" && t.category !== "salários") {
              return false;
            }

            return transactionMonth === month &&
              transactionYear === year;
          } catch (err) {
            return false;
          }
        });
      }

      // Calcular totais
      reportReceitas = reportTransactions
        .filter(t => t.type === "receita")
        .reduce((acc, t) => acc + (typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount), 0);

      reportDespesas = reportTransactions
        .filter(t => t.type === "despesa")
        .reduce((acc, t) => acc + (typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount), 0);

      // Preparar dados para a tabela
      let tableData = [];
      let tableHeaders = [];

      if (type === "payroll") {
        // Para relatório de folha de pagamento, precisamos buscar informações adicionais
        tableHeaders = ['Nome', 'Função', 'Salário Base', 'Vales', 'Salário Líquido', 'Forma de Pagamento'];

        // Criar um array para armazenar as promessas de busca de dados
        const dataPromises = reportTransactions.map(async (t) => {
          let name = "";
          let role = "";
          let paymentInfo = "";
          let category = ""; // Adicionar categoria para agrupamento

          // Formatar o valor
          const formattedAmount = typeof t.amount === 'string'
            ? `R$ ${parseFloat(t.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
            : `R$ ${t.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;

          // Formatar a data
          const formattedDate = new Date(t.date).toLocaleDateString('pt-BR');

          if (t.player_id) {
            try {
              // Buscar informações do jogador
              const playerData = await getPlayerById(clubId, t.player_id);

              // Pular jogadores inativos
              if (playerData.status === "inativo") {
                return null; // Retornar null para filtrar depois
              }

              name = playerData.name;
              role = "Jogador";
              // Usar a posição como categoria para jogadores
              category = playerData.position || "Sem Categoria";

              // Obter informações de pagamento (PIX tem preferência)
              if (playerData.bank_pix_key) {
                paymentInfo = `PIX: ${playerData.bank_pix_key}`;
              } else if (playerData.bank_account_number && playerData.bank_branch) {
                paymentInfo = `Banco: ${playerData.bank_name || ""}, Agência: ${playerData.bank_branch}, Conta: ${playerData.bank_account_number}`;
              } else {
                paymentInfo = "Não informado";
              }
            } catch (error) {
              console.error("Erro ao buscar informações do jogador:", error);
              name = t.description;
              paymentInfo = "Não disponível";
              category = "Sem Categoria";
            }
          } else if (t.collaborator_id) {
            try {
              // Buscar informações do colaborador
              const { data: collaboratorData, error } = await supabase
                .from("collaborators")
                .select("*")
                .eq("id", t.collaborator_id)
                .eq("club_id", clubId)
                .single();

              if (error) {
                throw error;
              }

              name = collaboratorData.full_name;
              role = collaboratorData.role;
              category = "Comissão Técnica"; // Categoria padrão para colaboradores

              // Obter informações de pagamento do campo bank_info (PIX tem preferência)
              if (collaboratorData.bank_info) {
                const bankInfo = collaboratorData.bank_info;
                if (bankInfo.pix) {
                  paymentInfo = `PIX: ${bankInfo.pix}`;
                } else if (bankInfo.account_number && bankInfo.agency) {
                  paymentInfo = `Banco: ${bankInfo.bank_name || ""}, Agência: ${bankInfo.agency}, Conta: ${bankInfo.account_number}`;
                } else {
                  paymentInfo = "Não informado";
                }
              } else {
                paymentInfo = "Não informado";
              }
            } catch (error) {
              console.error("Erro ao buscar informações do colaborador:", error);
              name = t.description;
              paymentInfo = "Não disponível";
              category = "Comissão Técnica";
            }
          } else {
            // Se não for jogador nem colaborador, usar a descrição
            name = t.description;
            role = "Outro";
            paymentInfo = "Não aplicável";
            category = "Outros";
          }

          // Calcular vales para esta pessoa no mês/ano do relatório
          let valeAmount = 0;
          let personId = "";
          let personType = "";

          if (t.player_id) {
            personId = t.player_id;
            personType = "player";
          } else if (t.collaborator_id) {
            personId = t.collaborator_id.toString();
            personType = "collaborator";
          }

          if (personId && personType) {
            try {
              // Buscar vales para esta pessoa no mês/ano do relatório
              const vales = await getSalaryAdvances(clubId, month || selectedMonth + 1, year || selectedYear, personType as 'player' | 'collaborator', personId);
              valeAmount = vales.reduce((total, vale) => total + vale.amount, 0);
            } catch (error) {
              console.error("Erro ao buscar vales:", error);
            }
          }

          // Calcular salário líquido
          const salaryValue = typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount;
          const salarioLiquido = salaryValue - valeAmount;

          const formattedVale = valeAmount > 0 ? `R$ ${valeAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : "-";
          const formattedSalarioLiquido = `R$ ${salarioLiquido.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;

          return [name, role, formattedAmount, formattedVale, formattedSalarioLiquido, paymentInfo, category]; // Incluir categoria no retorno
        });

        // Aguardar todas as promessas serem resolvidas
        const allData = await Promise.all(dataPromises);

        // Filtrar valores nulos (jogadores inativos)
        const rawData = allData.filter(item => item !== null);

        // Verificar se temos dados para mostrar
        if (rawData.length === 0) {
          // Se não houver transações, adicionar uma mensagem informativa
          tableData = [
            [
              {
                content: "Nenhuma transação de salário encontrada para este período",
                colSpan: 6, // Agora temos 6 colunas
                styles: {
                  fontStyle: 'italic',
                  halign: 'center',
                  textColor: [150, 150, 150]
                }
              }
            ]
          ];
        } else {
          // Agrupar por categoria
          const dataByCategory: Record<string, any[][]> = {};

          // Primeiro, agrupar os dados por categoria
          rawData.forEach(row => {
            const category = row[6]; // A categoria está na posição 6 agora
            if (!dataByCategory[category]) {
              dataByCategory[category] = [];
            }
            // Remover a categoria do array antes de adicionar aos dados da tabela
            dataByCategory[category].push(row.slice(0, 6)); // Agora temos 6 colunas (Nome, Função, Salário Base, Vales, Salário Líquido, Forma de Pagamento)
          });

          // Ordenar cada grupo por nome
          Object.keys(dataByCategory).forEach(category => {
            dataByCategory[category].sort((a, b) => a[0].localeCompare(b[0]));
          });

          // Criar uma lista de categorias ordenadas
          const orderedCategories = Object.keys(dataByCategory).sort((a, b) => {
            // Colocar "Sem Categoria" no início
            if (a === "Sem Categoria") return -1;
            if (b === "Sem Categoria") return 1;
            // Colocar "Comissão Técnica" depois de "Sem Categoria"
            if (a === "Comissão Técnica" && b !== "Sem Categoria") return -1;
            if (b === "Comissão Técnica" && a !== "Sem Categoria") return 1;
            // Ordenar as demais categorias alfabeticamente
            return a.localeCompare(b);
          });

          // Preparar os dados finais com cabeçalhos de seção
          tableData = [];

          // Para cada categoria, adicionar um cabeçalho e os dados
          orderedCategories.forEach(category => {
            // Definir cores diferentes para cada tipo de categoria
            let fillColor = [41, 128, 185]; // Azul padrão

            if (category === "Sem Categoria") {
              fillColor = [52, 152, 219]; // Azul claro
            } else if (category === "Comissão Técnica") {
              fillColor = [155, 89, 182]; // Roxo
            } else if (category === "Goleiro") {
              fillColor = [46, 204, 113]; // Verde
            } else if (category === "Zagueiro" || category === "Lateral") {
              fillColor = [52, 73, 94]; // Azul escuro
            } else if (category === "Volante" || category === "Meio-Campo") {
              fillColor = [230, 126, 34]; // Laranja
            } else if (category === "Atacante" || category === "Ponta") {
              fillColor = [231, 76, 60]; // Vermelho
            }

            // Adicionar uma linha de cabeçalho para a categoria
            tableData.push([
              {
                content: `${category}`,
                colSpan: 6, // Agora temos 6 colunas
                styles: {
                  fillColor: fillColor,
                  textColor: [255, 255, 255],
                  fontStyle: 'bold',
                  halign: 'center',
                  fontSize: 12,
                  cellPadding: 5
                }
              }
            ]);

            // Adicionar os dados da categoria
            tableData.push(...dataByCategory[category]);
          });
        }
      } else {
        // Para relatórios financeiros normais
        tableHeaders = ['Descrição', 'Categoria', 'Receita', 'Despesa', 'Data', 'Função'];

        tableData = reportTransactions.map(t => {
          // Determinar a função com base nos dados da transação
          let role = "";
          if (t.player_id) {
            role = t.category === "salários" ? "Jogador" : "";
          } else if (t.collaborator_id) {
            // Usar a função específica do colaborador se disponível
            role = t.collaborator_role || "Colaborador";
          }

          return [
            t.description,
            t.category,
            t.type === "receita" ? `R$ ${typeof t.amount === 'string' ? parseFloat(t.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 }) : t.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : "",
            t.type === "despesa" ? `R$ ${typeof t.amount === 'string' ? parseFloat(t.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 }) : t.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : "",
            new Date(t.date).toLocaleDateString('pt-BR'),
            role
          ];
        });
      }

      // Função para gerar o PDF com todos os dados
      const gerarPDFCompleto = (logoDataUrl?: string) => {
        // Criar um novo documento PDF
        const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

        // Adicionar título
        doc.setFontSize(18);
        doc.text(reportTitle, 14, 22);

        // Adicionar informações do clube
        doc.setFontSize(12);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

        // Adicionar logo se disponível
        if (logoDataUrl) {
          try {
            // Usar dimensões fixas para a logo para evitar problemas de escala
            // Posicionar no canto superior direito
            doc.addImage(logoDataUrl, 'PNG', 160, 15, 30, 20);
          } catch (logoError) {
            console.error("Erro ao adicionar logo ao PDF:", logoError);
          }
        }

        // Adicionar resumo financeiro
        doc.setFontSize(14);
        doc.text("Resumo Financeiro", 14, 40);

        doc.setFontSize(10);
        doc.text(`Receitas: R$ ${reportReceitas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 14, 48);
        doc.text(`Despesas: R$ ${reportDespesas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 14, 54);
        doc.text(`Saldo: R$ ${(reportReceitas - reportDespesas).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 14, 60);
        doc.text(`Folha de Pagamento: R$ ${folhaPagamento.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 14, 66);

        // Adicionar tabela de transações
        doc.setFontSize(14);
        if (type === "payroll") {
          doc.text("Folha de Pagamento por Categoria", 14, 76);
        } else {
          doc.text("Transações do Período", 14, 76);
        }

        // Adicionar a tabela ao PDF usando a função autoTable importada
        if (type === "payroll") {
          // Para relatório de folha de pagamento, usamos configurações especiais
          autoTable(doc, {
            startY: 80,
            head: [tableHeaders],
            body: tableData,
            theme: 'grid',
            headStyles: { fillColor: [0, 87, 183], textColor: [255, 255, 255], fontStyle: 'bold' },
            // Configurações especiais para lidar com células mescladas e estilos personalizados
            didParseCell: function(data) {
              // Aplicar estilos personalizados para células com conteúdo de objeto
              if (data.cell && data.cell.raw && typeof data.cell.raw === 'object' && data.cell.raw.content) {
                // Aplicar estilos personalizados da célula
                if (data.cell.raw.styles && data.cell.styles) {
                  Object.assign(data.cell.styles, data.cell.raw.styles);
                }

                // Aplicar mesclagem de células
                if (data.cell.raw.colSpan) {
                  data.cell.colSpan = data.cell.raw.colSpan;
                }

                // Definir o conteúdo da célula
                if (data.cell.raw.content) {
                  data.cell.text = [String(data.cell.raw.content)];
                }
              }
            }
          });

          // Obter a posição Y final da tabela
          const docWithTable = doc as jsPDFWithAutoTable;
          const finalY = docWithTable.lastAutoTable?.finalY || 120;

          // Adicionar resumo financeiro no final do relatório
          doc.setFontSize(14);
          // Verificar se finalY está definido
          const resumoY = finalY ? finalY + 20 : 120; // Usar um valor padrão se finalY não estiver definido
          doc.text("Resumo Financeiro", 14, resumoY);

          // Verificar se temos dados para mostrar no resumo
          const rawData = tableData; // Usar os dados da tabela como fonte para o resumo
          if (!rawData || rawData.length === 0) {
            // Se não houver transações, adicionar uma mensagem informativa no resumo
            autoTable(doc, {
              startY: resumoY + 5,
              head: [['Categoria', 'Valor', 'Percentual']],
              body: [
                [
                  {
                    content: "Nenhuma transação de salário encontrada para este período",
                    colSpan: 3,
                    styles: {
                      fontStyle: 'italic',
                      halign: 'center',
                      textColor: [150, 150, 150]
                    }
                  }
                ]
              ],
              theme: 'grid',
              headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255], fontStyle: 'bold' },
              // Configurações especiais para lidar com células com estilos personalizados
              didParseCell: function(data) {
                if (data.cell && data.cell.raw && typeof data.cell.raw === 'object' && data.cell.raw.styles && data.cell.styles) {
                  Object.assign(data.cell.styles, data.cell.raw.styles);
                }

                // Aplicar mesclagem de células
                if (data.cell && data.cell.raw && data.cell.raw.colSpan) {
                  data.cell.colSpan = data.cell.raw.colSpan;
                }

                // Definir o conteúdo da célula
                if (data.cell && data.cell.raw && data.cell.raw.content) {
                  data.cell.text = [String(data.cell.raw.content)];
                }
              }
            });
          } else {
            // Calcular valores por categoria
            const categorySummary: Record<string, number> = {};
            let totalFolha = 0;

            // Processar os dados brutos para calcular os totais por categoria
            if (rawData) rawData.forEach(row => {
              // Verificar se row é um array válido e tem elementos suficientes
              if (!Array.isArray(row) || row.length < 7) return; // Agora precisamos de 7 elementos (6 colunas + categoria)

              const category = row[6]; // A categoria está na posição 6 agora
              const amountStr = row[2]; // O valor do salário base está na posição 2

              // Verificar se amountStr é uma string válida antes de usar match
              if (typeof amountStr === 'string' && amountStr) {
                // Extrair o valor numérico da string formatada (ex: "R$ 1.000,00" -> 1000)
                const amountMatch = amountStr.match(/R\$\s*([\d.,]+)/);
                if (amountMatch && amountMatch[1]) {
                  const amountValue = parseFloat(amountMatch[1].replace(/\./g, '').replace(',', '.'));

                  if (!isNaN(amountValue) && category) {
                    if (!categorySummary[category]) {
                      categorySummary[category] = 0;
                    }

                    categorySummary[category] += amountValue;
                    totalFolha += amountValue;
                  }
                }
              }
            });

            // Criar tabela de resumo
            const summaryData = Object.entries(categorySummary).map(([category, amount]) => [
              category,
              `R$ ${amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
              `${((amount / totalFolha) * 100).toFixed(1)}%`
            ]);

            // Adicionar linha de total
            summaryData.push([
              { content: 'Total da Folha', styles: { fontStyle: 'bold' } },
              { content: `R$ ${totalFolha.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, styles: { fontStyle: 'bold' } },
              { content: '100%', styles: { fontStyle: 'bold' } }
            ]);

            // Adicionar tabela de resumo
            autoTable(doc, {
              startY: resumoY + 5,
              head: [['Categoria', 'Valor', 'Percentual']],
              body: summaryData,
              theme: 'grid',
              headStyles: { fillColor: [0, 87, 183], textColor: [255, 255, 255], fontStyle: 'bold' },
              // Configurações especiais para lidar com células com estilos personalizados
              didParseCell: function(data) {
                if (data.cell && data.cell.raw && typeof data.cell.raw === 'object' && data.cell.raw.styles && data.cell.styles) {
                  Object.assign(data.cell.styles, data.cell.raw.styles);
                }

                // Definir o conteúdo da célula se for um objeto
                if (data.cell && data.cell.raw && typeof data.cell.raw === 'object' && data.cell.raw.content) {
                  data.cell.text = [String(data.cell.raw.content)];
                }
              }
            });
          }
        } else {
          // Para relatórios financeiros normais
          autoTable(doc, {
            startY: 80,
            head: [tableHeaders],
            body: tableData,
            theme: 'striped',
            headStyles: { fillColor: [0, 87, 183], textColor: [255, 255, 255] }
          });
        }

        // Adicionar rodapé
        const pageCount = doc.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
          doc.setPage(i);
          doc.setFontSize(8);
          doc.text(
            `Relatório gerado em ${new Date().toLocaleDateString('pt-BR')} - Página ${i} de ${pageCount}`,
            14,
            doc.internal.pageSize.height - 10
          );
        }

        // Definir nome do arquivo
        const fileName = type === "month"
          ? `relatorio-financeiro-${months[month || selectedMonth].toLowerCase()}-${year || selectedYear}.pdf`
          : type === "payroll"
          ? `relatorio-folha-pagamento-${months[month || selectedMonth].toLowerCase()}-${year || selectedYear}.pdf`
          : `relatorio-financeiro-completo.pdf`;

        // Salvar o PDF
        doc.save(fileName);
      };

      // Verificar se há logo para adicionar
      if (clubInfo.logo_url) {
        try {
          // Usar o logo do localStorage se disponível (mais seguro)
          const logoFromStorage = localStorage.getItem("teamLogo");
          const logoUrl = logoFromStorage || clubInfo.logo_url;

          // Verificar se a URL é válida
          if (logoUrl && (logoUrl.startsWith('http') || logoUrl.startsWith('data:'))) {
            // Carregar a imagem primeiro para poder manipulá-la
            const img = new Image();
            img.crossOrigin = "Anonymous"; // Permitir carregamento cross-origin

            // Definir um timeout para evitar que a operação fique presa
            const timeoutId = setTimeout(() => {
              console.error("Timeout ao carregar a imagem do logo");
              gerarPDFCompleto(); // Gerar sem logo após timeout
            }, 5000); // 5 segundos de timeout

            // Definir um callback para quando a imagem for carregada
            img.onload = function() {
              // Limpar o timeout quando a imagem for carregada
              clearTimeout(timeoutId);
              try {
                // Criar um canvas para manipular a imagem
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Definir as dimensões do canvas
                canvas.width = img.width;
                canvas.height = img.height;

                // Desenhar a imagem no canvas (sem rotação)
                if (ctx) {
                  ctx.drawImage(img, 0, 0);

                  try {
                    // Converter o canvas para uma URL de dados
                    const dataUrl = canvas.toDataURL('image/png');

                    // Verificar se a URL de dados é válida
                    if (dataUrl && dataUrl.startsWith('data:image/')) {
                      // Gerar o PDF com a logo
                      gerarPDFCompleto(dataUrl);
                    } else {
                      console.error("URL de dados inválida para a logo");
                      gerarPDFCompleto();
                    }
                  } catch (dataUrlError) {
                    console.error("Erro ao converter canvas para URL de dados:", dataUrlError);
                    gerarPDFCompleto();
                  }
                } else {
                  // Se não conseguir obter o contexto do canvas, gerar sem logo
                  gerarPDFCompleto();
                }
              } catch (canvasError) {
                console.error("Erro ao processar imagem no canvas:", canvasError);
                gerarPDFCompleto(); // Gerar sem logo
              }
            };

            // Definir um handler para erros de carregamento da imagem
            img.onerror = function() {
              // Limpar o timeout quando ocorrer um erro
              clearTimeout(timeoutId);
              console.error("Erro ao carregar a imagem do logo");
              gerarPDFCompleto(); // Gerar sem logo
            };

            // Iniciar o carregamento da imagem
            img.src = logoUrl;

            // Retornar aqui para evitar que o código abaixo seja executado
            // O PDF será gerado no callback onload da imagem
            return;
          } else {
            // URL inválida, gerar sem logo
            gerarPDFCompleto();
          }
        } catch (logoError) {
          console.error("Erro ao adicionar logo do clube:", logoError);
          // Continuar sem o logo
          gerarPDFCompleto();
        }
      } else {
        // Sem logo, gerar o PDF normalmente
        gerarPDFCompleto();
      }
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Financeiro & Admin</h1>
          <p className="text-muted-foreground">
            Gestão financeira, contratos e pagamentos
          </p>
        </div>
        <div className="flex items-center gap-2">
          <select
            className="h-10 rounded-md border border-input bg-background px-3 py-2"
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
          >
            {months.map((month, index) => (
              <option key={index} value={index}>{month}</option>
            ))}
          </select>
          <select
            className="h-10 rounded-md border border-input bg-background px-3 py-2"
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
          >
            {Array.from({ length: 10 }, (_, i) => selectedYear - 5 + i).map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">Receitas (mês)</span>
                <TrendingUp className="h-4 w-4 text-green-600" />
              </div>
              <span className="text-2xl font-bold mt-1">{`R$ ${totalReceitas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}</span>
              <span className="text-xs text-green-600 mt-1">{`${variacaoReceitas >= 0 ? '+' : ''}${variacaoReceitas}% vs. mês anterior`}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">Despesas (mês)</span>
                <TrendingDown className="h-4 w-4 text-red-600" />
              </div>
              <span className="text-2xl font-bold mt-1">{`R$ ${totalDespesas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}</span>
              <span className="text-xs text-red-600 mt-1">{`${variacaoDespesas >= 0 ? '+' : ''}${variacaoDespesas}% vs. mês anterior`}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">Folha de Pagamento</span>
                <Users className="h-4 w-4 text-blue-600" />
              </div>
              <span className="text-2xl font-bold mt-1">{`R$ ${folhaPagamento.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}</span>
              <div className="text-xs text-blue-600 mt-1 space-y-1">
                <div>{`Salário Bruto: R$ ${(salarioJogadores + salarioColaboradores).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}</div>
                <div>{`Vales: R$ ${(valesJogadores + valesColaboradores).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}</div>
                <div>{`${salaries.length} contratos ativos`}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">Contas a Pagar (Total)</span>
                <DollarSign className="h-4 w-4 text-orange-600" />
              </div>
              <span className="text-2xl font-bold mt-1">{`R$ ${totalTodosPagamentos.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}</span>
              <span className="text-xs text-orange-600 mt-1">{`${totalPagamentosPendentes} pagamentos pendentes`}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="fluxo" onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="fluxo">Fluxo Financeiro</TabsTrigger>
          <TabsTrigger value="contas">Contas a Pagar</TabsTrigger>
          <TabsTrigger value="salarios">Salários</TabsTrigger>
          <TabsTrigger value="contratos">Contratos</TabsTrigger>
        </TabsList>

        <ReportDialog
          open={reportDialogOpen}
          onOpenChange={setReportDialogOpen}
          onGenerate={handleGenerateReport}
          currentMonth={selectedMonth}
          currentYear={selectedYear}
        />

        <TabsContent value="contas">
          <Card>
            <CardHeader className="pb-0 flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <div>
                <CardTitle>Contas a Pagar</CardTitle>
                <CardDescription>
                  Todos os pagamentos do clube - pendentes e realizados
                </CardDescription>
              </div>
              <div className="flex flex-wrap gap-2 mt-4 sm:mt-0">
                <Button variant="outline" size="sm" onClick={() => setAccountsReportDialogOpen(true)}>
                  <Download className="h-4 w-4 mr-2" />
                  Exportar Relatório
                </Button>
                <Button className="bg-team-blue hover:bg-blue-700" onClick={() => {
                  setSelectedAccount(null);
                  setContasDialogOpen(true);
                }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Nova Conta
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Buscar por descrição ou fornecedor/cliente"
                      className="pl-8"
                      value={accountDescriptionFilter}
                      onChange={(e) => setAccountDescriptionFilter(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={paymentSourceFilter} onValueChange={setPaymentSourceFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filtrar por origem" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="todos">Todas as origens</SelectItem>
                      <SelectItem value="account">Contas a Pagar</SelectItem>
                      <SelectItem value="transaction">Transações Diretas</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={accountStatusFilter} onValueChange={setAccountStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filtrar por status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="todos">Todos os status</SelectItem>
                      <SelectItem value="pendente">Pendente</SelectItem>
                      <SelectItem value="pago">Pago</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Descrição</TableHead>
                      <TableHead>Fornecedor/Cliente</TableHead>
                      <TableHead>Vencimento</TableHead>
                      <TableHead>Valor</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPayments.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-4">
                          Nenhum pagamento encontrado
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredPayments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <FileMinus className="h-4 w-4 text-red-600" />
                              <span className="font-medium">{payment.description}</span>
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              {payment.category && (
                                <span className="text-xs text-muted-foreground">
                                  {payment.category}
                                </span>
                              )}
                              <Badge variant="outline" className="text-xs">
                                {payment.source}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>{payment.supplier_client}</TableCell>
                          <TableCell>{formatDateWithoutTimezone(payment.date)}</TableCell>
                          <TableCell className="text-red-600">
                            R$ {typeof payment.amount === 'string'
                              ? parseFloat(payment.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })
                              : payment.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={
                                payment.status === "pendente"
                                  ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                  : "bg-green-50 text-green-700 border-green-200"
                              }
                            >
                              {payment.status === "pendente" ? "Pendente" : "Pago"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Abrir menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                {payment.type === 'account' && (
                                  <>
                                    <DropdownMenuItem onClick={() => {
                                      setSelectedAccount(payment.original);
                                      setAccountDetailsDialogOpen(true);
                                    }}>
                                      Detalhes
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => {
                                      setSelectedAccount(payment.original);
                                      setContasDialogOpen(true);
                                    }}>
                                      Editar
                                    </DropdownMenuItem>
                                    {payment.status === "pendente" && (
                                      <DropdownMenuItem onClick={async () => {
                                        try {
                                          await markAccountAsPaid(clubId, payment.original.id);
                                          toast({
                                            title: "Conta marcada como paga",
                                            description: "Status atualizado com sucesso.",
                                          });
                                        } catch (error) {
                                          console.error("Erro ao marcar conta:", error);
                                          toast({
                                            title: "Erro",
                                            description: "Ocorreu um erro ao atualizar o status da conta.",
                                            variant: "destructive",
                                          });
                                        }
                                      }}>
                                        Marcar como Pago
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuItem onClick={() => {
                                      setSelectedAccount(payment.original);
                                      setUploadReceiptDialogOpen(true);
                                    }}>
                                      Upload de Comprovante
                                    </DropdownMenuItem>
                                    {payment.original.receipt_url && (
                                      <DropdownMenuItem onClick={() => window.open(payment.original.receipt_url, "_blank")}>
                                        Baixar Comprovante
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      className="text-red-600"
                                      onClick={async () => {
                                        try {
                                          if (confirm(`Tem certeza que deseja excluir a conta "${payment.description}"?`)) {
                                            await deleteAccount(clubId, payment.original.id);
                                            toast({
                                              title: "Conta excluída",
                                              description: "A conta foi excluída com sucesso.",
                                            });
                                            await fetchAccounts(clubId);
                                          }
                                        } catch (error) {
                                          console.error("Erro ao excluir conta:", error);
                                          toast({
                                            title: "Erro",
                                            description: "Ocorreu um erro ao excluir a conta.",
                                            variant: "destructive",
                                          });
                                        }
                                      }}
                                    >
                                      Excluir
                                    </DropdownMenuItem>
                                  </>
                                )}
                                {payment.type === 'transaction' && (
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedTransaction(payment.original);
                                    setTransactionDetailsOpen(true);
                                  }}>
                                    Ver detalhes
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fluxo">
          <Card>
            <CardHeader className="pb-0 flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <div>
                <CardTitle>Fluxo Financeiro</CardTitle>
                <CardDescription>
                  Receitas e despesas do clube - {months[selectedMonth]} {selectedYear}
                </CardDescription>
              </div>
              <div className="flex flex-wrap gap-2 mt-4 sm:mt-0">
                <Button variant="outline" size="sm" onClick={handleOpenReportDialog}>
                  <Download className="h-4 w-4 mr-2" />
                  Exportar Relatório
                </Button>
                <Button variant="outline" size="sm" onClick={() => setAccountsPayableGeneralReportDialogOpen(true)}>
                  <Download className="h-4 w-4 mr-2" />
                  Contas a Pagar Geral
                </Button>
                <Button variant="outline" size="sm" onClick={() => setCashFlowReportDialogOpen(true)}>
                  <Download className="h-4 w-4 mr-2" />
                  Fluxo de Caixa
                </Button>
                <Button variant="outline" size="sm" onClick={() => setAccountsPayablePeriodReportDialogOpen(true)}>
                  <Download className="h-4 w-4 mr-2" />
                  Contas por Período
                </Button>
                <div className="flex justify-end mb-4">
                  <Button className="bg-team-blue hover:bg-blue-700" onClick={() => setNovaTransacaoDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Nova Transação
                  </Button>
                </div>
                <NovaTransacaoDialog open={novaTransacaoDialogOpen} onOpenChange={setNovaTransacaoDialogOpen} clubId={clubId} />
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="overflow-x-auto">
                <div className="mb-4 flex flex-wrap gap-2">
                  <div className="flex-1">
                    <Input
                      placeholder="Filtrar por descrição..."
                      className="max-w-xs"
                      value={descriptionFilter}
                      onChange={(e) => {
                        setDescriptionFilter(e.target.value);
                      }}
                    />
                  </div>
                  <div>
                    <select
                      className="h-10 rounded-md border border-input bg-background px-3 py-2"
                      value={categoryFilter}
                      onChange={(e) => {
                        setCategoryFilter(e.target.value);
                      }}
                    >
                      <option value="">Todas as categorias</option>
                      <option value="salários">Salários</option>
                      <option value="patrocínios">Patrocínios</option>
                      <option value="material">Material</option>
                      <option value="viagens">Viagens</option>
                      <option value="outros">Outros</option>
                    </select>
                  </div>
                  <div>
                    <select
                      className="h-10 rounded-md border border-input bg-background px-3 py-2"
                      value={paymentStatusFilter}
                      onChange={(e) => {
                        setPaymentStatusFilter(e.target.value);
                      }}
                    >
                      <option value="todos">Todos os status</option>
                      <option value="pagos_despesas">Pagamento Efetuado (Despesas)</option>
                      <option value="pagos">Pagos</option>
                      <option value="pendentes">Pendentes</option>
                    </select>
                  </div>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Descrição</TableHead>
                      <TableHead className="hidden md:table-cell">Categoria</TableHead>
                      <TableHead>Valor</TableHead>
                      <TableHead className="hidden md:table-cell">Data</TableHead>
                      <TableHead className="hidden md:table-cell">Tipo</TableHead>
                      <TableHead className="hidden lg:table-cell">Função</TableHead>
                      <TableHead className="hidden md:table-cell">Status</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {transaction.type === "receita" ? (
                              <FilePlus className="h-4 w-4 text-green-600" />
                            ) : (
                              <FileMinus className="h-4 w-4 text-red-600" />
                            )}
                            {transaction.description}
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">{transaction.category}</TableCell>
                        <TableCell className={transaction.type === "receita" ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
                          {typeof transaction.amount === 'string'
                            ? `R$ ${parseFloat(transaction.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
                            : `R$ ${transaction.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {transaction.date ? formatDateWithoutTimezone(transaction.date) : '-'}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <Badge
                            variant="outline"
                            className={
                              transaction.type === "receita"
                                ? "border-green-200 bg-green-50 text-green-800"
                                : "border-red-200 bg-red-50 text-red-800"
                            }
                          >
                            {transaction.type === "receita" ? "Receita" : "Despesa"}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          {transaction.player_name ? (
                            <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-800">
                              Jogador
                            </Badge>
                          ) : transaction.collaborator_id ? (
                            <Badge variant="outline" className="border-purple-200 bg-purple-50 text-purple-800">
                              {transaction.collaborator_role || "Colaborador"}
                            </Badge>
                          ) : transaction.medical_professional_id ? (
                            <Badge variant="outline" className="border-teal-200 bg-teal-50 text-teal-800">
                              {transaction.medical_professional_role || "Médico"}
                            </Badge>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {transaction.payment_status === 'paid' ? (
                            <Badge variant="outline" className="border-green-200 bg-green-50 text-green-800">
                              Pago
                            </Badge>
                          ) : (
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="border-yellow-200 bg-yellow-50 text-yellow-800">
                                Pendente
                              </Badge>
                              {transaction.type === 'despesa' && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-7 px-2 text-xs"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    markAsPaid(clubId, transaction.id);
                                  }}
                                >
                                  <FileCheck className="h-3 w-3 mr-1" />
                                  Pago
                                </Button>
                              )}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                setSelectedTransaction(transaction);
                                setTransactionDetailsOpen(true);
                              }}>
                                Ver detalhes
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                setSelectedTransaction(transaction);
                                setEditTransactionOpen(true);
                              }}>
                                Editar transação
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {transaction.player_name && (
                                <DropdownMenuItem>Ver perfil da pessoa</DropdownMenuItem>
                              )}
                              <DropdownMenuItem onClick={() => {
                                setSelectedTransaction(transaction);
                                setReceiptDialogOpen(true);
                              }}>
                                Gerar comprovante
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                setSelectedTransaction(transaction);
                                setBonusDialogOpen(true);
                              }}>
                                Registrar bônus
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => {
                                  setSelectedTransaction(transaction);
                                  setDeleteDialogOpen(true);
                                }}
                              >
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="salarios">
          <Card>
            <CardHeader className="pb-0 flex flex-col sm:flex-row justify-between">
              <div>
                <CardTitle>Salários e Bonificações</CardTitle>
                <CardDescription>
                  Gestão de folha de pagamento de jogadores e colaboradores
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button className="bg-team-blue hover:bg-blue-700" onClick={() => { setEditingSalary(null); setSalaryDialogOpen(true); }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Adicionar
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Função</TableHead>
                      <TableHead>Salário Base</TableHead>
                      <TableHead className="hidden md:table-cell">Vales</TableHead>
                      <TableHead className="hidden md:table-cell">Salário Líquido</TableHead>
                      <TableHead className="hidden lg:table-cell">Início Contrato</TableHead>
                      <TableHead className="hidden lg:table-cell">Fim Contrato</TableHead>
                      <TableHead className="hidden md:table-cell">Status</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {/* Renderizar transações de salários de jogadores do mês/ano selecionado */}
                    {financialTransactions
                      .filter(transaction => {
                        // Filtrar apenas transações de salários de jogadores do mês/ano selecionado
                        if (transaction.category !== "salários" || transaction.type !== "despesa" || !transaction.player_id) return false;

                        try {
                          const dateParts = transaction.date.split('-');
                          const transactionMonth = parseInt(dateParts[1]) - 1;
                          const transactionYear = parseInt(dateParts[0]);
                          return transactionMonth === selectedMonth && transactionYear === selectedYear;
                        } catch (err) {
                          return false;
                        }
                      })
                      .map((transaction) => {
                        // Extrair nome do jogador da descrição
                        const playerName = transaction.description.split(' - ')[1] || transaction.player_id;

                        return (
                          <TableRow key={`player-transaction-${transaction.id}`}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarFallback className="bg-team-blue text-white">
                                    {playerName ? playerName.slice(0, 2).toUpperCase() : '--'}
                                  </AvatarFallback>
                                </Avatar>
                                <span>{playerName}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-800">
                                Jogador
                              </Badge>
                            </TableCell>
                            <TableCell>R$ {(typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</TableCell>
                            <TableCell className="hidden md:table-cell text-red-600">
                              {(() => {
                                const valeJogador = salaryAdvances
                                  .filter(advance =>
                                    advance.person_type === 'player' &&
                                    advance.person_id === transaction.player_id &&
                                    advance.month === selectedMonth + 1 &&
                                    advance.year === selectedYear &&
                                    advance.status === 'active'
                                  )
                                  .reduce((total, advance) => total + advance.amount, 0);
                                return valeJogador > 0 ? `R$ ${valeJogador.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : '-';
                              })()}
                            </TableCell>
                            <TableCell className="hidden md:table-cell font-medium">
                              {(() => {
                                const valeJogador = salaryAdvances
                                  .filter(advance =>
                                    advance.person_type === 'player' &&
                                    advance.person_id === transaction.player_id &&
                                    advance.month === selectedMonth + 1 &&
                                    advance.year === selectedYear &&
                                    advance.status === 'active'
                                  )
                                  .reduce((total, advance) => total + advance.amount, 0);
                                const salarioLiquido = (typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount) - valeJogador;
                                return `R$ ${salarioLiquido.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
                              })()}
                            </TableCell>
                            <TableCell className="hidden lg:table-cell">{transaction.date}</TableCell>
                            <TableCell className="hidden lg:table-cell">-</TableCell>
                            <TableCell className="hidden md:table-cell">
                              <Badge
                                variant="outline"
                                className={transaction.payment_status === 'paid' ? "border-green-200 bg-green-50 text-green-800" : "border-yellow-200 bg-yellow-50 text-yellow-800"}
                              >
                                {transaction.payment_status === 'paid' ? 'Pago' : 'Pendente'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => {
                                    setAdicionarValeDialogOpen(true);
                                    setSelectedSalary({
                                      player_id: transaction.player_id,
                                      player_name: playerName,
                                      value: transaction.amount
                                    });
                                  }}>
                                    Adicionar Vale
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>Ver Detalhes</DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        );
                      })}

                    {/* Renderizar transações de salários de colaboradores do mês/ano selecionado */}
                    {financialTransactions
                      .filter(transaction => {
                        // Filtrar apenas transações de salários de colaboradores do mês/ano selecionado
                        if (transaction.category !== "salários" || transaction.type !== "despesa" || !transaction.collaborator_id) return false;

                        try {
                          const dateParts = transaction.date.split('-');
                          const transactionMonth = parseInt(dateParts[1]) - 1;
                          const transactionYear = parseInt(dateParts[0]);
                          return transactionMonth === selectedMonth && transactionYear === selectedYear;
                        } catch (err) {
                          return false;
                        }
                      })
                      .map((transaction) => {
                        // Extrair nome do colaborador da descrição
                        const collaboratorName = transaction.description.split(' - ')[1] || `Colaborador ${transaction.collaborator_id}`;

                        return (
                          <TableRow key={`collaborator-transaction-${transaction.id}`}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarFallback className="bg-purple-600 text-white">
                                    {collaboratorName ? collaboratorName.slice(0, 2).toUpperCase() : '--'}
                                  </AvatarFallback>
                                </Avatar>
                                <span>{collaboratorName}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="border-purple-200 bg-purple-50 text-purple-800">
                                Colaborador
                              </Badge>
                            </TableCell>
                            <TableCell>R$ {(typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</TableCell>
                            <TableCell className="hidden md:table-cell text-red-600">
                              {(() => {
                                const valeColaborador = salaryAdvances
                                  .filter(advance =>
                                    advance.person_type === 'collaborator' &&
                                    advance.person_id === transaction.collaborator_id &&
                                    advance.month === selectedMonth + 1 &&
                                    advance.year === selectedYear &&
                                    advance.status === 'active'
                                  )
                                  .reduce((total, advance) => total + advance.amount, 0);
                                return valeColaborador > 0 ? `R$ ${valeColaborador.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : '-';
                              })()}
                            </TableCell>
                            <TableCell className="hidden md:table-cell font-medium">
                              {(() => {
                                const valeColaborador = salaryAdvances
                                  .filter(advance =>
                                    advance.person_type === 'collaborator' &&
                                    advance.person_id === transaction.collaborator_id &&
                                    advance.month === selectedMonth + 1 &&
                                    advance.year === selectedYear &&
                                    advance.status === 'active'
                                  )
                                  .reduce((total, advance) => total + advance.amount, 0);
                                const salarioLiquido = (typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount) - valeColaborador;
                                return `R$ ${salarioLiquido.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
                              })()}
                            </TableCell>
                            <TableCell className="hidden lg:table-cell">{transaction.date}</TableCell>
                            <TableCell className="hidden lg:table-cell">-</TableCell>
                            <TableCell className="hidden md:table-cell">
                              <Badge
                                variant="outline"
                                className={transaction.payment_status === 'paid' ? "border-green-200 bg-green-50 text-green-800" : "border-yellow-200 bg-yellow-50 text-yellow-800"}
                              >
                                {transaction.payment_status === 'paid' ? 'Pago' : 'Pendente'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => {
                                    setAdicionarValeDialogOpen(true);
                                    setSelectedSalary({
                                      id: transaction.collaborator_id,
                                      person_type: 'collaborator',
                                      person_name: collaboratorName,
                                      person_role: 'Colaborador',
                                      value: transaction.amount
                                    });
                                  }}>
                                    Adicionar Vale
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => {
                                    // Buscar colaborador completo para abrir finanças
                                    const collaborator = collaborators.find(c => c.id === transaction.collaborator_id);
                                    if (collaborator) {
                                      setSelectedCollaborator(collaborator);
                                      setCollaboratorFinanceDialogOpen(true);
                                    }
                                  }}>Ver Finanças</DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        );
                      })}

                    {/* Mensagem quando não há transações de salários */}
                    {financialTransactions.filter(transaction => {
                      if (transaction.category !== "salários" || transaction.type !== "despesa") return false;
                      try {
                        const dateParts = transaction.date.split('-');
                        const transactionMonth = parseInt(dateParts[1]) - 1;
                        const transactionYear = parseInt(dateParts[0]);
                        return transactionMonth === selectedMonth && transactionYear === selectedYear;
                      } catch (err) {
                        return false;
                      }
                    }).length === 0 && (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                          Nenhuma transação de salário encontrada para {months[selectedMonth]} de {selectedYear}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
          {salaryDialogOpen && (
            <SalaryDialog
              open={salaryDialogOpen}
              onOpenChange={setSalaryDialogOpen}
              onSave={async (salary) => {
                if (editingSalary) {
                  await updatePlayerSalary(clubId, editingSalary.id, salary);
                } else {
                  await createPlayerSalary(clubId, salary);
                }
                const sal = await getSalaries(clubId);
                setSalaries(sal);
              }}
              initialData={editingSalary}
              clubId={clubId}
            />
          )}
        </TabsContent>

        <TabsContent value="contratos">
          <Card>
            <CardHeader className="pb-0 flex flex-col sm:flex-row justify-between">
              <div>
                <CardTitle>Contratos e Patrocínios</CardTitle>
                <CardDescription>
                  Gestão de contratos, parcerias e acordos comerciais
                </CardDescription>
              </div>
              <Button className="mt-4 sm:mt-0 bg-team-blue hover:bg-blue-700" onClick={() => setContractDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Novo Contrato
              </Button>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Título</TableHead>
                      <TableHead className="hidden md:table-cell">Tipo</TableHead>
                      <TableHead>Valor</TableHead>
                      <TableHead className="hidden lg:table-cell">Início</TableHead>
                      <TableHead className="hidden lg:table-cell">Término</TableHead>
                      <TableHead className="hidden md:table-cell">Status</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {contracts.map((contract) => (
                      <TableRow key={contract.id}>
                        <TableCell className="font-medium">{contract.title}</TableCell>
                        <TableCell className="hidden md:table-cell">{contract.type}</TableCell>
                        <TableCell>{contract.value}</TableCell>
                        <TableCell className="hidden lg:table-cell">{contract.start_date || contract.startDate || '-'}</TableCell>
                        <TableCell className="hidden lg:table-cell">{contract.end_date || contract.endDate || '-'}</TableCell>
                        <TableCell className="hidden md:table-cell">
                          <Badge
                            variant="outline"
                            className={
                              contract.status === "Ativo"
                                ? "border-green-200 bg-green-50 text-green-800"
                                : "border-amber-200 bg-amber-50 text-amber-800"
                            }
                          >
                            {contract.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>Ver contrato</DropdownMenuItem>
                              <DropdownMenuItem onClick={() => { setContractDialogOpen(true); setEditingContract(contract); }}>Editar detalhes</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Histórico de pagamentos</DropdownMenuItem>
                              <DropdownMenuItem>Renovar contrato</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600" onClick={async () => {
                                await deleteContract(clubId, contract.id);
                                const updatedContracts = await getContracts(clubId);
                                setContracts(updatedContracts);
                              }}>
                                Rescindir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
          <ContractDialog
            open={contractDialogOpen}
            onOpenChange={(open) => {
              setContractDialogOpen(open);
              if (!open) setEditingContract(null);
            }}
            clubId={clubId}
            contract={editingContract}
            onSave={async () => {
              const contracts = await getContracts(clubId);
              setContracts(contracts);
            }}
          />
        </TabsContent>
      </Tabs>

      {/* Modais para operações financeiras */}
      <TransactionDetailsDialog
        open={transactionDetailsOpen}
        onOpenChange={setTransactionDetailsOpen}
        transaction={selectedTransaction}
      />

      <EditTransactionDialog
        open={editTransactionOpen}
        onOpenChange={setEditTransactionOpen}
        transaction={selectedTransaction}
        clubId={clubId}
      />

      <ReceiptDialog
        open={receiptDialogOpen}
        onOpenChange={setReceiptDialogOpen}
        transaction={selectedTransaction}
        clubId={clubId}
      />

      <BonusDialog
        open={bonusDialogOpen}
        onOpenChange={setBonusDialogOpen}
        transaction={selectedTransaction}
        clubId={clubId}
      />

      <DeleteTransactionDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        transaction={selectedTransaction}
        clubId={clubId}
      />

      {/* Modais para contas a pagar/receber */}
      <ContasDialog
        open={contasDialogOpen}
        onOpenChange={setContasDialogOpen}
        clubId={clubId}
        account={selectedAccount}
      />

      <UploadReceiptDialog
        open={uploadReceiptDialogOpen}
        onOpenChange={setUploadReceiptDialogOpen}
        account={selectedAccount}
        clubId={clubId}
      />

      <AccountDetailsDialog
        open={accountDetailsDialogOpen}
        onOpenChange={setAccountDetailsDialogOpen}
        account={selectedAccount}
      />

      <AccountsReportDialog
        open={accountsReportDialogOpen}
        onOpenChange={setAccountsReportDialogOpen}
        accounts={filteredAccounts}
        currentMonth={selectedMonth}
        currentYear={selectedYear}
      />

      {/* Diálogo para adicionar vale (adiantamento) */}
      {selectedSalary && (
        <AdicionarValeDialog
          open={adicionarValeDialogOpen}
          onOpenChange={setAdicionarValeDialogOpen}
          personId={selectedSalary.person_type === 'collaborator' ? selectedSalary.id : selectedSalary.player_id}
          personType={selectedSalary.person_type || "player"}
          personName={selectedSalary.person_name || selectedSalary.player_name}
          personRole={selectedSalary.person_role || "Atleta"}
          salary={parseFloat(selectedSalary.value)}
          onSuccess={async () => {
            // Recarregar adiantamentos após adicionar um novo
            const advances = await getSalaryAdvances(clubId, selectedMonth + 1, selectedYear);
            setSalaryAdvances(advances);

            // Atualizar transações financeiras
            fetchTransactions(clubId);

            toast({
              title: "Vale registrado",
              description: "O adiantamento foi registrado com sucesso",
            });
          }}
        />
      )}

      {/* Diálogo de finanças do colaborador */}
      {selectedCollaborator && (
        <ColaboradorFinanceiroDialog
          open={collaboratorFinanceDialogOpen}
          onOpenChange={setCollaboratorFinanceDialogOpen}
          clubId={clubId}
          collaborator={selectedCollaborator}
          onSuccess={async () => {
            // Recarregar dados após mudanças
            const [sal, col, con, advances] = await Promise.all([
              getSalaries(clubId),
              getCollaborators(clubId),
              getContracts(clubId),
              getSalaryAdvances(clubId, selectedMonth + 1, selectedYear)
            ]);
            setSalaries(sal);
            setCollaborators(col);
            setContracts(con);
            setSalaryAdvances(advances);
            fetchTransactions(clubId);
          }}
        />
      )}

      {/* Novos diálogos de relatórios */}
      <AccountsPayableGeneralReportDialog
        open={accountsPayableGeneralReportDialogOpen}
        onOpenChange={setAccountsPayableGeneralReportDialogOpen}
      />

      <CashFlowReportDialog
        open={cashFlowReportDialogOpen}
        onOpenChange={setCashFlowReportDialogOpen}
      />

      <AccountsPayablePeriodReportDialog
        open={accountsPayablePeriodReportDialogOpen}
        onOpenChange={setAccountsPayablePeriodReportDialogOpen}
      />
    </div>
  );
}
