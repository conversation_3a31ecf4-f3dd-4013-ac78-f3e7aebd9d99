import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { format } from "date-fns";

// Mapeamento de nomes de cores para códigos HEX
const COLOR_MAP: Record<string, string> = {
  'red': '#E53E3E',    // Vermelho
  'blue': '#3182CE',   // Azul
  'purple': '#805AD5', // Roxo
  'green': '#38A169',  // Verde
  'yellow': '#D69E2E', // <PERSON><PERSON>
  'pink': '#D53F8C',  // <PERSON>
  'orange': '#DD6B20', // <PERSON><PERSON>
  'teal': '#319795',   // Verde-água
  'gray': '#718096',   // Cinza
  'black': '#1A202C'   // Preto
};

// Função auxiliar para converter cor (HEX ou nome) para RGB
function hexToRgb(color: string | undefined | null) {
  // Se não houver cor definida ou for inválida, retorna azul padrão
  if (!color || typeof color !== 'string') {
    console.warn('Cor inválida, usando azul padrão');
    return { r: 41, g: 128, b: 185 };
  }
  
  try {
    // Verifica se é um nome de cor conhecido
    const hexColor = COLOR_MAP[color.toLowerCase()] || color;
    
    // Remove o # se presente
    const hexClean = hexColor.replace(/^#/, '');
    
    // Garante que o valor tem 6 caracteres
    const hexValid = hexClean.length === 3 
      ? hexClean.split('').map(c => c + c).join('') 
      : hexClean;
    
    // Converte para RGB
    const r = parseInt(hexValid.substring(0, 2), 16) || 0;
    const g = parseInt(hexValid.substring(2, 4), 16) || 0;
    const b = parseInt(hexValid.substring(4, 6), 16) || 0;
    
    // Valida os valores
    if (isNaN(r) || isNaN(g) || isNaN(b)) {
      throw new Error('Valores RGB inválidos');
    }
    
    return { r, g, b };
  } catch (error) {
    console.error('Erro ao converter cor:', color, error);
    return { r: 41, g: 128, b: 185 }; // Retorna azul padrão em caso de erro
  }
}
import { ptBR } from "date-fns/locale";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api";
import { getAllPendingPayables, ConsolidatedPayable } from "@/api/financialReports";

interface AccountsPayableGeneralReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Interface for jsPDF with autoTable
interface jsPDFWithAutoTable extends Omit<jsPDF, 'internal'> {
  autoTable: typeof autoTable;
  lastAutoTable?: any; // Adicionado para resolver o erro de tipagem
  internal: {
    getNumberOfPages: () => number;
    pageSize: {
      width: number;
      height: number;
      getWidth: () => number;
      getHeight: () => number;
    };
    scaleFactor: number;
    pages: number[];
    events: any;
    getEncryptor: (objectId: number) => (data: string) => string;
  };
}

export function AccountsPayableGeneralReportDialog({
  open,
  onOpenChange
}: AccountsPayableGeneralReportDialogProps) {
  const [department, setDepartment] = useState("todos");
  const [category, setCategory] = useState("todos");
  const [loading, setLoading] = useState(false);
  const clubId = useCurrentClubId();

  const handleGenerate = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Get club info
      const clubInfo = await getClubInfo(clubId);

      // Get all pending payables
      const allPayables = await getAllPendingPayables(clubId);

      // Apply filters
      let filteredPayables = allPayables;

      if (department !== "todos") {
        filteredPayables = filteredPayables.filter(p => p.department === department);
      }

      if (category !== "todos") {
        filteredPayables = filteredPayables.filter(p => p.category === category);
      }

      // Group by department
      const payablesByDepartment: Record<string, ConsolidatedPayable[]> = {};
      filteredPayables.forEach(payable => {
        const dept = payable.department || 'Outros';
        if (!payablesByDepartment[dept]) {
          payablesByDepartment[dept] = [];
        }
        payablesByDepartment[dept].push(payable);
      });

      // Create PDF
      const doc = new jsPDF("portrait", "mm", "a4") as jsPDFWithAutoTable;

      // Configurações de layout
      const pageWidth = doc.internal.pageSize.width;
      const margin = 15;
      const logoSize = 20;
      
      // Adiciona o título à esquerda
      doc.setFontSize(18);
      const title = "Relatório Geral de Contas a Pagar";
      doc.text(title, margin, 20);
      
      // Adiciona as informações do clube abaixo do título
      doc.setFontSize(12);
      let currentY = 30; // Posição Y inicial após o título
      
      doc.text(clubInfo.name, margin, currentY);
      currentY += 5;
      
      if (clubInfo.address) {
        doc.setFontSize(10);
        doc.text(clubInfo.address, margin, currentY);
        currentY += 5;
      }
      
      if (clubInfo.phone) {
        doc.text(`Telefone: ${clubInfo.phone}`, margin, currentY);
        currentY += 5;
      }
      
      // Adiciona o logo à direita, alinhado com o título
      if (clubInfo.logo_url) {
        try {
          const logoResponse = await fetch(clubInfo.logo_url);
          const logoBlob = await logoResponse.blob();
          const logoUrl = URL.createObjectURL(logoBlob);
          
          // Posiciona o logo à direita, alinhado com o título
          const logoX = pageWidth - margin - logoSize;
          doc.addImage(logoUrl, 'JPEG', logoX, 10, logoSize, logoSize);
        } catch (error) {
          console.error("Erro ao carregar logo:", error);
          // Se falhar, continua sem o logo
        }
      }

      // Add generation date
      doc.setFontSize(10);
      const startY = currentY + 5; // Usa a posição atual + 5mm de margem
      doc.text(`Gerado em: ${format(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}`, 15, startY);

      currentY = startY + 10; // Atualiza a posição Y para o conteúdo seguinte

      // Add summary
      const totalAmount = filteredPayables.reduce((sum, p) => sum + p.amount, 0);
      const totalCount = filteredPayables.length;

      doc.setFontSize(12);
      doc.text("Resumo Geral:", 15, currentY);
      currentY += 8;

      doc.setFontSize(10);
      doc.text(`Total de Contas: ${totalCount}`, 15, currentY);
      currentY += 5;
      doc.text(`Valor Total: R$ ${totalAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, currentY);
      currentY += 15;

      // Generate tables by department
      const sortedDepartments = Object.keys(payablesByDepartment).sort();

      for (const dept of sortedDepartments) {
        const deptPayables = payablesByDepartment[dept];
        const deptTotal = deptPayables.reduce((sum, p) => sum + p.amount, 0);

        // Department header - Usando as cores do clube
        doc.setFontSize(14);
        
        // Definir cores do tema
        const primaryColor = clubInfo.primary_color ? 
          hexToRgb(clubInfo.primary_color) : {r: 41, g: 128, b: 185};
        const secondaryColor = clubInfo.secondary_color ? 
          hexToRgb(clubInfo.secondary_color) : 
          {r: Math.max(0, primaryColor.r - 30), g: Math.max(0, primaryColor.g - 30), b: Math.max(0, primaryColor.b - 30)};
        
        console.log('Cores do tema:', { primaryColor, secondaryColor });
        
        // Cabeçalho do departamento
        try {
          console.log('Definindo cor de preenchimento:', { r: primaryColor.r, g: primaryColor.g, b: primaryColor.b });
          doc.setFillColor(primaryColor.r, primaryColor.g, primaryColor.b);
          
          const rectX = 15;
          const rectY = currentY;
          const rectWidth = pageWidth - 30;
          const rectHeight = 8;
          
          console.log('Desenhando retângulo:', { x: rectX, y: rectY, width: rectWidth, height: rectHeight });
          doc.rect(rectX, rectY, rectWidth, rectHeight, 'F');
          
          console.log('Definindo cor do texto para branco');
          doc.setTextColor(255, 255, 255);
          
          const text = `${dept} (${deptPayables.length} itens - R$ ${deptTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`;
          const textX = 17;
          const textY = currentY + 5;
          
          console.log('Desenhando texto:', { text, x: textX, y: textY });
          doc.text(text, textX, textY);
          
          console.log('Resetando cor do texto para preto');
          doc.setTextColor(0, 0, 0);
          
          currentY += 12;
          console.log('Nova posição Y:', currentY);
        } catch (error) {
          console.error('Erro ao desenhar cabeçalho do departamento:', error);
          throw error;
        }

        // Prepare table data
        const tableData = deptPayables.map(payable => [
          payable.name,
          payable.description,
          format(new Date(payable.due_date || payable.transaction_date), "dd/MM/yyyy", { locale: ptBR }),
          payable.pix_key || 'Não informado',
          payable.role || '-',
          `R$ ${payable.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
        ]);

        // Add table
        autoTable(doc, {
          head: [["Nome", "Descrição", "Vencimento", "Chave PIX", "Função", "Valor"]],
          body: tableData,
          startY: currentY,
          styles: {
            fontSize: 8,
            cellPadding: 2,
          },
          headStyles: {
            fillColor: [secondaryColor.r, secondaryColor.g, secondaryColor.b] as [number, number, number],
            textColor: 255,
            fontStyle: 'bold' as const,
            halign: 'center'
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240],
          },
          margin: { left: 15, right: 15 },
        });

        currentY = (doc as any).lastAutoTable.finalY + 10;

        // Check if we need a new page
        if (currentY > 250) {
          doc.addPage();
          currentY = 20;
        }
      }

      // Add page numbers
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(`Página ${i} de ${pageCount}`, pageWidth / 2, doc.internal.pageSize.height - 10, { align: "center" });
      }

      // Save the PDF
      const reportTitle = `Contas_a_Pagar_Geral_${format(new Date(), "dd-MM-yyyy")}.pdf`;
      doc.save(reportTitle);

      toast({
        title: "Relatório gerado",
        description: "O relatório geral de contas a pagar foi gerado com sucesso.",
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o relatório.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Get unique departments and categories for filters
  const [payables, setPayables] = useState<ConsolidatedPayable[]>([]);

  // Load payables for filter options
  React.useEffect(() => {
    if (open && clubId) {
      getAllPendingPayables(clubId).then(setPayables).catch(console.error);
    }
  }, [open, clubId]);

  const uniqueDepartments = Array.from(new Set(payables.map(p => p.department).filter(Boolean)));
  const uniqueCategories = Array.from(new Set(payables.map(p => p.category).filter(Boolean)));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Relatório Geral de Contas a Pagar</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <Label htmlFor="department">Departamento</Label>
              <Select value={department} onValueChange={setDepartment}>
                <SelectTrigger id="department">
                  <SelectValue placeholder="Selecione o departamento" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos os Departamentos</SelectItem>
                  {uniqueDepartments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="category">Categoria</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Selecione a categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todas as Categorias</SelectItem>
                  {uniqueCategories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? "Gerando..." : "Gerar Relatório"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
