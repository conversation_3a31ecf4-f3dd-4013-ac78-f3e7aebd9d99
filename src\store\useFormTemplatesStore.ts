import { create } from "zustand";
import {
  ClubFormTemplate,
  getClubFormTemplates,
  getActiveFormTemplates,
  getFormTemplateById,
  createFormTemplate,
  updateFormTemplate,
  deleteFormTemplate,
  toggleTemplateStatus,
  generateFormTemplatePDF,
  FormTemplateInput
} from "@/api/clubFormTemplates";
import { getClubInfo } from "@/api/api";

interface FormTemplatesState {
  templates: ClubFormTemplate[];
  activeTemplates: ClubFormTemplate[];
  selectedTemplate: ClubFormTemplate | null;
  loading: boolean;
  error: string | null;

  // Actions
  fetchTemplates: (clubId: number, userId: string, formType?: string) => Promise<void>;
  fetchActiveTemplates: (clubId: number, formType?: string) => Promise<void>;
  fetchTemplateById: (clubId: number, templateId: number, userId: string) => Promise<void>;
  addTemplate: (clubId: number, template: Omit<FormTemplateInput, "club_id">, userId: string) => Promise<void>;
  updateTemplate: (clubId: number, templateId: number, updates: Partial<Omit<FormTemplateInput, "club_id" | "created_by">>, userId: string) => Promise<void>;
  removeTemplate: (clubId: number, templateId: number, userId: string) => Promise<void>;
  toggleStatus: (clubId: number, templateId: number, userId: string) => Promise<void>;
  generatePDF: (template: ClubFormTemplate, clubId: number) => Promise<Blob>;
  setSelectedTemplate: (template: ClubFormTemplate | null) => void;
  clearError: () => void;
}

export const useFormTemplatesStore = create<FormTemplatesState>((set, get) => ({
  templates: [],
  activeTemplates: [],
  selectedTemplate: null,
  loading: false,
  error: null,

  fetchTemplates: async (clubId: number, userId: string, formType?: string) => {
    set({ loading: true, error: null });
    try {
      const templates = await getClubFormTemplates(clubId, userId, formType);
      set({ templates, loading: false });
    } catch (err: unknown) {
      set({ 
        error: err instanceof Error ? err.message : "Erro ao carregar templates", 
        loading: false 
      });
    }
  },

  fetchActiveTemplates: async (clubId: number, formType?: string) => {
    set({ loading: true, error: null });
    try {
      const activeTemplates = await getActiveFormTemplates(clubId, formType);
      set({ activeTemplates, loading: false });
    } catch (err: unknown) {
      set({ 
        error: err instanceof Error ? err.message : "Erro ao carregar templates ativos", 
        loading: false 
      });
    }
  },

  fetchTemplateById: async (clubId: number, templateId: number, userId: string) => {
    set({ loading: true, error: null });
    try {
      const template = await getFormTemplateById(clubId, templateId, userId);
      set({ selectedTemplate: template, loading: false });
    } catch (err: unknown) {
      set({ 
        error: err instanceof Error ? err.message : "Erro ao carregar template", 
        loading: false 
      });
    }
  },

  addTemplate: async (clubId: number, template: Omit<FormTemplateInput, "club_id">, userId: string) => {
    set({ loading: true, error: null });
    try {
      const newTemplate = await createFormTemplate(clubId, template, userId);
      const currentTemplates = get().templates;
      set({ 
        templates: [newTemplate, ...currentTemplates], 
        loading: false 
      });
    } catch (err: unknown) {
      set({ 
        error: err instanceof Error ? err.message : "Erro ao criar template", 
        loading: false 
      });
      throw err;
    }
  },

  updateTemplate: async (clubId: number, templateId: number, updates: Partial<Omit<FormTemplateInput, "club_id" | "created_by">>, userId: string) => {
    set({ loading: true, error: null });
    try {
      const updatedTemplate = await updateFormTemplate(clubId, templateId, updates, userId);
      const currentTemplates = get().templates;
      const updatedTemplates = currentTemplates.map(template => 
        template.id === templateId ? updatedTemplate : template
      );
      set({ 
        templates: updatedTemplates, 
        selectedTemplate: updatedTemplate,
        loading: false 
      });
    } catch (err: unknown) {
      set({ 
        error: err instanceof Error ? err.message : "Erro ao atualizar template", 
        loading: false 
      });
      throw err;
    }
  },

  removeTemplate: async (clubId: number, templateId: number, userId: string) => {
    set({ loading: true, error: null });
    try {
      await deleteFormTemplate(clubId, templateId, userId);
      const currentTemplates = get().templates;
      const filteredTemplates = currentTemplates.filter(template => template.id !== templateId);
      set({ 
        templates: filteredTemplates, 
        selectedTemplate: null,
        loading: false 
      });
    } catch (err: unknown) {
      set({ 
        error: err instanceof Error ? err.message : "Erro ao excluir template", 
        loading: false 
      });
      throw err;
    }
  },

  toggleStatus: async (clubId: number, templateId: number, userId: string) => {
    set({ loading: true, error: null });
    try {
      const updatedTemplate = await toggleTemplateStatus(clubId, templateId, userId);
      const currentTemplates = get().templates;
      const updatedTemplates = currentTemplates.map(template => 
        template.id === templateId ? updatedTemplate : template
      );
      set({ 
        templates: updatedTemplates, 
        loading: false 
      });
    } catch (err: unknown) {
      set({ 
        error: err instanceof Error ? err.message : "Erro ao alterar status do template", 
        loading: false 
      });
      throw err;
    }
  },

  generatePDF: async (template: ClubFormTemplate, clubId: number): Promise<Blob> => {
    set({ loading: true, error: null });
    try {
      const clubInfo = await getClubInfo(clubId);
      const pdfBlob = await generateFormTemplatePDF(template, clubInfo);
      set({ loading: false });
      return pdfBlob;
    } catch (err: unknown) {
      set({ 
        error: err instanceof Error ? err.message : "Erro ao gerar PDF", 
        loading: false 
      });
      throw err;
    }
  },

  setSelectedTemplate: (template: ClubFormTemplate | null) => {
    set({ selectedTemplate: template });
  },

  clearError: () => {
    set({ error: null });
  }
}));
