/* Enhanced Form Styles */

/* Form containers */
.enhanced-form-container {
  @apply space-y-6 p-6 bg-background rounded-lg border;
}

.enhanced-form-section {
  @apply space-y-4 p-4 bg-muted/20 rounded-md border border-muted;
}

/* Form fields with better spacing and visual hierarchy */
.form-field-group {
  @apply space-y-2;
}

.form-field-label {
  @apply text-sm font-medium text-foreground;
}

.form-field-required {
  @apply text-destructive ml-1;
}

.form-field-description {
  @apply text-xs text-muted-foreground mt-1;
}

.form-field-error {
  @apply text-xs text-destructive font-medium mt-1;
}

/* Enhanced input styles */
.enhanced-input {
  @apply w-full px-3 py-2 border border-input bg-background rounded-md text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  @apply disabled:cursor-not-allowed disabled:opacity-50;
  @apply placeholder:text-muted-foreground;
  transition: all 0.2s ease-in-out;
}

.enhanced-input:hover:not(:disabled) {
  @apply border-primary/50;
}

.enhanced-input.error {
  @apply border-destructive focus:ring-destructive;
}

.enhanced-input.success {
  @apply border-green-500 focus:ring-green-500;
}

/* Enhanced select styles */
.enhanced-select-trigger {
  @apply enhanced-input cursor-pointer;
}

.enhanced-select-trigger:hover:not(:disabled) {
  @apply border-primary/50;
}

/* Enhanced textarea styles */
.enhanced-textarea {
  @apply enhanced-input resize-none min-h-[80px];
}

/* Form grid layouts */
.form-grid-1 {
  @apply grid grid-cols-1 gap-4;
}

.form-grid-2 {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-grid-3 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.form-grid-4 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
}

/* Form buttons */
.form-button-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90;
  @apply px-4 py-2 rounded-md font-medium text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  transition: all 0.2s ease-in-out;
}

.form-button-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  @apply px-4 py-2 rounded-md font-medium text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  transition: all 0.2s ease-in-out;
}

.form-button-outline {
  @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  @apply px-4 py-2 rounded-md font-medium text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  transition: all 0.2s ease-in-out;
}

/* Loading states */
.form-loading {
  @apply opacity-50 pointer-events-none;
}

.form-loading-spinner {
  @apply animate-spin inline-block w-4 h-4 mr-2;
}

/* Form validation states */
.form-field-valid {
  @apply border-green-500 focus:ring-green-500;
}

.form-field-invalid {
  @apply border-destructive focus:ring-destructive;
}

/* Form sections with better visual separation */
.form-section-header {
  @apply flex items-center gap-2 pb-2 border-b border-muted;
}

.form-section-title {
  @apply text-lg font-semibold text-primary;
}

.form-section-description {
  @apply text-sm text-muted-foreground;
}

/* Enhanced dialog forms */
.enhanced-dialog-form {
  @apply max-w-4xl max-h-[95vh] overflow-hidden flex flex-col;
}

.enhanced-dialog-header {
  @apply pb-4 border-b;
}

.enhanced-dialog-title {
  @apply text-xl font-semibold flex items-center gap-2;
}

.enhanced-dialog-content {
  @apply flex-1 overflow-auto space-y-6 py-4;
}

.enhanced-dialog-footer {
  @apply pt-4 border-t bg-muted/20;
}

/* Status indicators */
.status-indicator {
  @apply w-2 h-2 rounded-full;
}

.status-indicator.active {
  @apply bg-green-500;
}

.status-indicator.inactive {
  @apply bg-gray-400;
}

.status-indicator.primary {
  @apply bg-primary;
}

.status-indicator.warning {
  @apply bg-yellow-500;
}

.status-indicator.error {
  @apply bg-destructive;
}

/* Form tabs */
.form-tabs-list {
  @apply grid grid-cols-5 mb-4 bg-muted/50;
}

.form-tab-trigger {
  @apply data-[state=active]:bg-primary data-[state=active]:text-primary-foreground;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .form-grid-2,
  .form-grid-3,
  .form-grid-4 {
    @apply grid-cols-1;
  }
  
  .enhanced-dialog-form {
    @apply max-w-[95vw] max-h-[95vh];
  }
}

/* Animation improvements */
.form-field-group {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus improvements */
.enhanced-input:focus,
.enhanced-textarea:focus,
.enhanced-select-trigger:focus {
  @apply ring-2 ring-primary ring-offset-2;
}

/* Error state improvements */
.form-field-error {
  animation: shake 0.3s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}
