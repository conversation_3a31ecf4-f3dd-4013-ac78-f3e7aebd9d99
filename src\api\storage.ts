import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";

/**
 * Faz upload de uma imagem de logo do clube para o Supabase Storage
 * @param clubId ID do clube
 * @param file Arquivo de imagem
 * @returns URL da imagem
 */
/**
 * Faz upload de uma imagem de convocação para o Supabase Storage
 */
export async function uploadCallupImage(
  clubId: string | number,
  file: File,
  imageType: string
): Promise<string> {
  try {
    const timestamp = new Date().getTime();
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `${imageType}-${clubId}-${timestamp}.${fileExtension}`;
    const filePath = `callups/club-${clubId}/${fileName}`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('profileimages')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true,
      });

    if (uploadError) {
      console.error('Erro ao fazer upload da imagem:', uploadError);
      throw new Error(`Erro ao fazer upload da imagem: ${uploadError.message}`);
    }

    // Obter a URL pública da imagem
    const { data: { publicUrl } } = supabase.storage
      .from('profileimages')
      .getPublicUrl(filePath);

    console.log('Imagem enviada com sucesso:', publicUrl);
    return publicUrl;
  } catch (error) {
    console.error('Erro no upload da imagem de convocação:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido ao fazer upload da imagem';
    throw new Error(errorMessage);
  }
}

export async function uploadClubLogo(clubId: string, file: File): Promise<string> {
  try {
    // Verificar se o arquivo é uma imagem
    if (!file.type.startsWith("image/")) {
      throw new Error("O arquivo deve ser uma imagem");
    }

    // Limitar tamanho do arquivo (2MB)
    const MAX_SIZE = 2 * 1024 * 1024; // 2MB
    if (file.size > MAX_SIZE) {
      throw new Error("A imagem deve ter no máximo 2MB");
    }

    // Gerar nome único para o arquivo
    const fileExt = file.name.split(".").pop();
    const fileName = `logo-${clubId}-${uuidv4()}.${fileExt}`;
    const filePath = `club-${clubId}/${fileName}`;

    // Fazer upload do arquivo
    const { error } = await supabase.storage
      .from("profileimages") // Usando o bucket existente
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
      });

    if (error) {
      throw new Error(`Erro ao fazer upload do logo: ${error.message}`);
    }

    // Obter URL pública do arquivo
    const { data: urlData } = supabase.storage
      .from("profileimages") // Usando o bucket existente
      .getPublicUrl(filePath);

    return urlData.publicUrl;
  } catch (error) {
    console.error("Erro ao fazer upload do logo do clube:", error);
    const errorMessage = error instanceof Error ? error.message : "Erro ao fazer upload do logo do clube";
    throw new Error(errorMessage);
  }
}

/**
 * Faz upload de uma imagem de perfil para o Supabase Storage
 * @param userId ID do usuário
 * @param file Arquivo de imagem
 * @returns URL da imagem
 */
export async function uploadProfileImage(userId: string, file: File): Promise<string> {
  try {
    // Verificar se o arquivo é uma imagem
    if (!file.type.startsWith("image/")) {
      throw new Error("O arquivo deve ser uma imagem");
    }

    // Limitar tamanho do arquivo (2MB)
    const MAX_SIZE = 2 * 1024 * 1024; // 2MB
    if (file.size > MAX_SIZE) {
      throw new Error("A imagem deve ter no máximo 2MB");
    }

    // Gerar nome único para o arquivo
    const fileExt = file.name.split(".").pop();
    const fileName = `${userId}-${uuidv4()}.${fileExt}`;
    const filePath = `${userId}/${fileName}`;

    // Fazer upload do arquivo
    const { error } = await supabase.storage
      .from("profileimages") // Nome do bucket no Supabase
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
      });

    if (error) {
      throw new Error(`Erro ao fazer upload da imagem: ${error.message}`);
    }

    // Obter URL pública do arquivo
    const { data: urlData } = supabase.storage
      .from("profileimages") // Nome do bucket no Supabase
      .getPublicUrl(filePath);

    return urlData.publicUrl;
  } catch (error) {
    console.error("Erro ao fazer upload da imagem de perfil:", error);
    const errorMessage = error instanceof Error ? error.message : "Erro ao fazer upload da imagem de perfil";
    throw new Error(errorMessage);
  }
}

/**
 * Faz upload de um documento de jogador para o Supabase Storage
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @param documentType Tipo do documento
 * @param file Arquivo do documento
 * @returns URL do documento
 */
export async function uploadPlayerDocument(
  clubId: string | number,
  playerId: string,
  documentType: string,
  file: File
): Promise<string> {
  try {
    // Limitar tamanho do arquivo (5MB)
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB
    if (file.size > MAX_SIZE) {
      throw new Error("O documento deve ter no máximo 5MB");
    }

    // Gerar nome único para o arquivo
    const fileExt = file.name.split(".").pop();
    const fileName = `${documentType}-${uuidv4()}.${fileExt}`;
    const filePath = `${clubId}/${playerId}/${fileName}`;

    // Fazer upload do arquivo com metadados
    const { error } = await supabase.storage
      .from("playerdocuments") // Nome do bucket no Supabase
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
        contentType: file.type,
        metadata: {
          club_id: clubId.toString(),
          player_id: playerId,
          document_type: documentType,
        },
      });

    if (error) {
      throw new Error(`Erro ao fazer upload do documento: ${error.message}`);
    }

    // Obter URL pública do arquivo
    const { data: urlData } = supabase.storage
      .from("playerdocuments") // Nome do bucket no Supabase
      .getPublicUrl(filePath);

    return urlData.publicUrl;
  } catch (error) {
    console.error("Erro ao fazer upload do documento:", error);
    const errorMessage = error instanceof Error ? error.message : "Erro ao fazer upload do documento";
    throw new Error(errorMessage);
  }
}

interface PlayerDocument {
  id: number;
  club_id: number;
  player_id: string;
  document_type: string;
  file_url: string;
  status: 'pending' | 'verified' | 'rejected';
  uploaded_at: string;
  verified_at?: string;
  verified_by?: string;
  rejection_reason?: string;
  player_name?: string;
}

/**
 * Registra um documento de jogador no banco de dados
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @param documentType Tipo do documento
 * @param fileUrl URL do arquivo
 * @returns Documento registrado
 */
export async function registerPlayerDocument(
  clubId: string | number,
  playerId: string,
  documentType: string,
  fileUrl: string
): Promise<PlayerDocument> {
  try {
    // Converte clubId para número se for string
    const numericClubId = typeof clubId === 'string' ? parseInt(clubId, 10) : clubId;
    
    // Chama a função RPC que gerencia a exclusão de documentos rejeitados
    const { data, error } = await supabase.rpc('register_player_document', {
      p_club_id: numericClubId,
      p_player_id: playerId,
      p_document_type: documentType,
      p_file_url: fileUrl
    });

    if (error) {
      throw new Error(`Erro ao registrar documento: ${error.message}`);
    }

    if (!data) {
      throw new Error('Nenhum dado retornado ao registrar documento');
    }

    return data as PlayerDocument;
  } catch (error: unknown) {
    console.error("Erro ao registrar documento:", error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido ao registrar documento';
    throw new Error(errorMessage);
  }
}

/**
 * Obtém os documentos de um jogador
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @returns Lista de documentos
 */
export async function getPlayerDocuments(clubId: string, playerId: string): Promise<any[]> {
  try {
    const { data, error } = await supabase
      .from("player_documents")
      .select("*")
      .eq("club_id", clubId)
      .eq("player_id", playerId);

    if (error) {
      throw new Error(`Erro ao obter documentos: ${error.message}`);
    }

    return data || [];
  } catch (error: any) {
    console.error("Erro ao obter documentos:", error);
    throw new Error(error.message || "Erro ao obter documentos");
  }
}

/**
 * Verifica um documento de jogador
 * @param documentId ID do documento
 * @param userId ID do usuário que está verificando
 * @param status Status da verificação ('verified' ou 'rejected')
 * @returns Documento atualizado
 */
export async function verifyPlayerDocument(
  documentId: string,
  userId: string,
  status: "verified" | "rejected"
): Promise<any> {
  try {
    const { data, error } = await supabase
      .from("player_documents")
      .update({
        status,
        verified_at: new Date().toISOString(),
        verified_by: userId,
      })
      .eq("id", documentId)
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao verificar documento: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error("Erro ao verificar documento:", error);
    throw new Error(error.message || "Erro ao verificar documento");
  }
}

/**
 * Exclui um documento de jogador
 * @param documentId ID do documento
 * @returns true se o documento foi excluído com sucesso
 */
export async function deletePlayerDocument(documentId: string): Promise<boolean> {
  try {
    // Primeiro, obter o documento para saber o caminho do arquivo
    const { data: document, error: getError } = await supabase
      .from("player_documents")
      .select("*")
      .eq("id", documentId)
      .single();

    if (getError || !document) {
      throw new Error(`Erro ao obter documento: ${getError?.message || "Documento não encontrado"}`);
    }

    // Extrair o caminho do arquivo da URL
    const fileUrl = document.file_url;
    const filePath = fileUrl.split("/").slice(-3).join("/");

    // Excluir o arquivo do storage
    const { error: storageError } = await supabase.storage
      .from("playerdocuments") // Nome do bucket no Supabase (sem underscore)
      .remove([filePath]);

    if (storageError) {
      console.error("Erro ao excluir arquivo do storage:", storageError);
      // Continuar mesmo se houver erro ao excluir o arquivo
    }

    // Excluir o registro do banco de dados
    const { error: dbError } = await supabase
      .from("player_documents")
      .delete()
      .eq("id", documentId);

    if (dbError) {
      throw new Error(`Erro ao excluir documento: ${dbError.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Erro ao excluir documento:", error);
    throw new Error(error.message || "Erro ao excluir documento");
  }
}

/**
 * Faz upload de um certificado médico para o Supabase Storage
 * @param clubId ID do clube
 * @param professionalId ID do profissional médico
 * @param file Arquivo do certificado
 * @returns URL do certificado
 */
export async function uploadMedicalCertificate(
  clubId: number,
  professionalId: number,
  file: File
): Promise<string> {
  try {
    // Limitar tamanho do arquivo (5MB)
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB
    if (file.size > MAX_SIZE) {
      throw new Error("O certificado deve ter no máximo 5MB");
    }

    // Verificar tipo de arquivo
    const allowedTypes = [
      "application/pdf",
      "image/jpeg",
      "image/png",
      "image/jpg",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];

    if (!allowedTypes.includes(file.type)) {
      throw new Error("Formato de arquivo não suportado");
    }

    // Gerar nome único para o arquivo
    const fileExt = file.name.split(".").pop();
    const fileName = `certificate-${uuidv4()}.${fileExt}`;
    const filePath = `${clubId}/medical/${professionalId}/${fileName}`;

    // Fazer upload do arquivo com metadados
    const { error } = await supabase.storage
      .from("playerdocuments") // Usando o mesmo bucket dos documentos de jogadores
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
        contentType: file.type,
        metadata: {
          club_id: clubId.toString(),
          professional_id: professionalId.toString(),
          document_type: "certificate",
        },
      });

    if (error) {
      throw new Error(`Erro ao fazer upload do certificado: ${error.message}`);
    }

    // Obter URL pública do arquivo
    const { data: urlData } = supabase.storage
      .from("playerdocuments")
      .getPublicUrl(filePath);

    return urlData.publicUrl;
  } catch (error) {
    console.error("Erro ao fazer upload do certificado:", error);
    const errorMessage = error instanceof Error ? error.message : "Erro ao fazer upload do certificado";
    throw new Error(errorMessage);
  }
}

/**
 * Upload a medical exam file to Supabase Storage
 * @param clubId The club ID
 * @param examId The exam ID
 * @param file The exam file
 * @returns The URL of the uploaded file
 */
export async function uploadExamFile(
  clubId: number,
  examId: number,
  file: File
): Promise<string> {
  try {
    // Limit file size (10MB)
    const MAX_SIZE = 10 * 1024 * 1024; // 10MB
    if (file.size > MAX_SIZE) {
      throw new Error("O arquivo deve ter no máximo 10MB");
    }

    // Check file type
    const allowedTypes = [
      "application/pdf",
      "image/jpeg",
      "image/png",
      "image/jpg",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/dicom",
    ];

    if (!allowedTypes.includes(file.type)) {
      throw new Error("Formato de arquivo não suportado");
    }

    // Generate unique filename
    const fileExt = file.name.split(".").pop();
    const fileName = `exam-${examId}-${uuidv4()}.${fileExt}`;
    const filePath = `${clubId}/medical/exams/${examId}/${fileName}`;

    // Upload file with metadata
    const { error } = await supabase.storage
      .from("playerdocuments") // Using the same bucket as player documents
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
        contentType: file.type,
        metadata: {
          club_id: clubId.toString(),
          exam_id: examId.toString(),
          document_type: "exam",
        },
      });

    if (error) {
      throw new Error(`Erro ao fazer upload do exame: ${error.message}`);
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from("playerdocuments")
      .getPublicUrl(filePath);

    return urlData.publicUrl;
  } catch (error) {
    console.error("Erro ao fazer upload do exame:", error);
    const errorMessage = error instanceof Error ? error.message : "Erro ao fazer upload do exame";
    throw new Error(errorMessage);
  }
}

/**
 * Upload a digital signature to Supabase Storage
 * @param clubId The club ID
 * @param signatureId A unique identifier for the signature (e.g., "treatment_123")
 * @param file The signature file (PNG)
 * @returns The URL of the uploaded signature
 */
export async function uploadSignature(
  clubId: number,
  signatureId: string,
  file: File
): Promise<string> {
  try {
    // Limit file size (2MB)
    const MAX_SIZE = 2 * 1024 * 1024; // 2MB
    if (file.size > MAX_SIZE) {
      throw new Error("A assinatura deve ter no máximo 2MB");
    }

    // Check file type
    if (!file.type.startsWith("image/")) {
      throw new Error("O arquivo deve ser uma imagem");
    }

    // Generate unique filename
    const fileExt = file.name.split(".").pop() || "png";
    const fileName = `signature-${signatureId}-${uuidv4()}.${fileExt}`;
    const filePath = `${clubId}/medical/signatures/${fileName}`;

    // Upload file
    const { error } = await supabase.storage
      .from("profileimages") // Using the profile images bucket for signatures
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
        contentType: file.type,
      });

    if (error) {
      throw new Error(`Erro ao fazer upload da assinatura: ${error.message}`);
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from("profileimages")
      .getPublicUrl(filePath);

    return urlData.publicUrl;
  } catch (error) {
    console.error("Erro ao fazer upload da assinatura:", error);
    const errorMessage = error instanceof Error ? error.message : "Erro ao fazer upload da assinatura";
    throw new Error(errorMessage);
  }
}
