import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, FileText, Download } from "lucide-react";
import type { TrainingReportOptions } from "@/utils/trainingReportGenerator";

interface TrainingReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGenerate: (options: TrainingReportOptions) => Promise<void>;
  loading?: boolean;
}

export function TrainingReportDialog({ 
  open, 
  onOpenChange, 
  onGenerate, 
  loading = false 
}: TrainingReportDialogProps) {
  const [options, setOptions] = useState<TrainingReportOptions>({
    includeStatistics: true,
    includeGoals: true,
    includeExercises: true,
    includeUpcoming: true,
    includeCompleted: true,
    period: {
      year: new Date().getFullYear(),
      month: undefined
    }
  });

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i);
  const months = [
    { value: 1, label: 'Janeiro' },
    { value: 2, label: 'Fevereiro' },
    { value: 3, label: 'Março' },
    { value: 4, label: 'Abril' },
    { value: 5, label: 'Maio' },
    { value: 6, label: 'Junho' },
    { value: 7, label: 'Julho' },
    { value: 8, label: 'Agosto' },
    { value: 9, label: 'Setembro' },
    { value: 10, label: 'Outubro' },
    { value: 11, label: 'Novembro' },
    { value: 12, label: 'Dezembro' }
  ];

  const handleOptionChange = (key: keyof TrainingReportOptions, value: any) => {
    setOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handlePeriodChange = (key: 'year' | 'month', value: any) => {
    setOptions(prev => ({
      ...prev,
      period: {
        ...prev.period,
        [key]: value === 'all' ? undefined : value
      }
    }));
  };

  const handleGenerate = async () => {
    try {
      await onGenerate(options);
      onOpenChange(false);
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Relatório de Treinamentos
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Período */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Período</Label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-xs text-muted-foreground">Ano</Label>
                <Select 
                  value={options.period?.year?.toString() || currentYear.toString()} 
                  onValueChange={(value) => handlePeriodChange('year', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map(year => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Mês</Label>
                <Select 
                  value={options.period?.month?.toString() || 'all'} 
                  onValueChange={(value) => handlePeriodChange('month', value === 'all' ? undefined : parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os meses</SelectItem>
                    {months.map(month => (
                      <SelectItem key={month.value} value={month.value.toString()}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Seções a incluir */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Seções a incluir</Label>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="statistics"
                  checked={options.includeStatistics}
                  onCheckedChange={(checked) => handleOptionChange('includeStatistics', checked)}
                />
                <Label htmlFor="statistics" className="text-sm">
                  Estatísticas e resumo
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="goals"
                  checked={options.includeGoals}
                  onCheckedChange={(checked) => handleOptionChange('includeGoals', checked)}
                />
                <Label htmlFor="goals" className="text-sm">
                  Objetivos de treinamento
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="upcoming"
                  checked={options.includeUpcoming}
                  onCheckedChange={(checked) => handleOptionChange('includeUpcoming', checked)}
                />
                <Label htmlFor="upcoming" className="text-sm">
                  Treinamentos agendados
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="completed"
                  checked={options.includeCompleted}
                  onCheckedChange={(checked) => handleOptionChange('includeCompleted', checked)}
                />
                <Label htmlFor="completed" className="text-sm">
                  Treinamentos concluídos
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="exercises"
                  checked={options.includeExercises}
                  onCheckedChange={(checked) => handleOptionChange('includeExercises', checked)}
                />
                <Label htmlFor="exercises" className="text-sm">
                  Banco de exercícios
                </Label>
              </div>
            </div>
          </div>

          {/* Preview do relatório */}
          <div className="bg-muted/50 p-3 rounded-md">
            <div className="flex items-start gap-2">
              <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div className="text-xs text-muted-foreground">
                <div className="font-medium mb-1">Relatório de Treinamentos</div>
                <div>
                  Período: {options.period?.month ? months.find(m => m.value === options.period?.month)?.label + '/' : ''}{options.period?.year || currentYear}
                </div>
                <div className="mt-1">
                  Seções: {[
                    options.includeStatistics && 'Estatísticas',
                    options.includeGoals && 'Objetivos',
                    options.includeUpcoming && 'Agendados',
                    options.includeCompleted && 'Concluídos',
                    options.includeExercises && 'Exercícios'
                  ].filter(Boolean).join(', ')}
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Gerando...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Gerar Relatório
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
