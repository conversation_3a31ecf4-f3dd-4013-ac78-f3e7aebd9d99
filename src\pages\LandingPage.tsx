import { useEffect, useRef } from "react";
 import { <PERSON> } from "react-router-dom";
 import { gsap } from "gsap";
 import { ScrollTrigger } from "gsap/ScrollTrigger";
 import { motion } from "framer-motion";
 import { Check, TrendingUp, Shield, Calendar, Users, Award, ChevronRight } from "lucide-react";
 import { Button } from "@/components/ui/button";

//  Muito feia
 // Registrar plugins do GSAP
 gsap.registerPlugin(ScrollTrigger);

 export default function LandingPage() {
   const headerRef = useRef<HTMLDivElement>(null);
   const featuresRef = useRef<HTMLDivElement>(null);
   const pricingRef = useRef<HTMLDivElement>(null);

   useEffect(() => {
     // Animação do header
     const headerTl = gsap.timeline();
     headerTl.from(".hero-title", {
       y: 50,
       opacity: 0,
       duration: 1,
       ease: "power3.out",
     });
     headerTl.from(".hero-subtitle", {
       y: 30,
       opacity: 0,
       duration: 0.8,
       ease: "power3.out",
     }, "-=0.6");
     headerTl.from(".hero-cta", {
       y: 20,
       opacity: 0,
       duration: 0.6,
       ease: "power3.out",
     }, "-=0.4");
     headerTl.from(".hero-image", {
       scale: 0.9,
       opacity: 0,
       duration: 1,
       ease: "power2.out",
     }, "-=0.6");

     // Animação dos recursos
     const featureItems = gsap.utils.toArray(".feature-item");
     featureItems.forEach((item, i) => {
       gsap.from(item, {
         scrollTrigger: {
           trigger: item as Element,
           start: "top 80%",
         },
         y: 50,
         opacity: 0,
         duration: 0.8,
         delay: i * 0.15,
       });
     });

     // Animação dos planos
     gsap.from(".pricing-title", {
       scrollTrigger: {
         trigger: pricingRef.current,
         start: "top 80%",
       },
       y: 30,
       opacity: 0,
       duration: 0.8,
     });

     const pricingCards = gsap.utils.toArray(".pricing-card");
     pricingCards.forEach((card, i) => {
       gsap.from(card, {
         scrollTrigger: {
           trigger: card as Element,
           start: "top 85%",
         },
         y: 40,
         opacity: 0,
         duration: 0.8,
         delay: i * 0.2,
       });
     });

     return () => {
       // Limpar instâncias do ScrollTrigger
       ScrollTrigger.getAll().forEach(trigger => trigger.kill());
     };
   }, []);

   const fadeInUpVariants = {
     hidden: { opacity: 0, y: 20 },
     visible: {
       opacity: 1,
       y: 0,
       transition: { duration: 0.6 }
     }
   };

   return (
     <div className="flex flex-col min-h-screen bg-gradient-to-b from-primary/5 to-white">
       {/* Header/Navigation */}
       <header className="px-6 py-4 md:px-12 lg:px-20 border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
         <div className="max-w-7xl mx-auto flex justify-between items-center">
           <div className="flex items-center gap-2">
             <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
               <span className="text-white font-bold">GDN</span>
             </div>
             <span className="font-bold text-xl text-primary">Game Day Nexus</span>
           </div>
           <nav className="hidden md:flex items-center gap-6">
             <a href="#features" className="text-gray-600 hover:text-primary transition-colors">Recursos</a>
             <a href="#pricing" className="text-gray-600 hover:text-primary transition-colors">Planos</a>
             <a href="#testimonials" className="text-gray-600 hover:text-primary transition-colors">Depoimentos</a>
             <Link to="/login" className="text-primary hover:text-primary/80 transition-colors font-medium">Login</Link>
             <Link to="/">
               <Button className="bg-primary hover:bg-primary/90">Entre em contato</Button>
             </Link>
           </nav>
           <div className="md:hidden">
             <Button variant="ghost" size="icon">
               <ChevronRight className="h-6 w-6" />
             </Button>
           </div>
         </div>
       </header>

       {/* Hero Section */}
       <section ref={headerRef} className="py-16 md:py-24 lg:py-32 px-6 md:px-12 lg:px-20">
         <div className="max-w-7xl mx-auto grid md:grid-cols-2 gap-12 items-center">
           <div>
             <h1 className="hero-title text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
               Gestão completa para seu <span className="text-team-blue">clube de futebol</span>
             </h1>
             <p className="hero-subtitle mt-6 text-xl text-gray-600 max-w-xl">
               Gerencie jogadores, partidas, treinamentos, finanças e mais em uma única plataforma integrada.
             </p>
             <div className="hero-cta mt-10 flex flex-col sm:flex-row gap-4">
               <Link to="/">
                 <Button size="lg" className="bg-team-blue hover:bg-team-blue/90 text-white font-medium px-8">
                   Começar agora
                 </Button>
               </Link>
               <Link to="/login">
                 <Button size="lg" variant="outline" className="border-team-blue text-team-blue hover:bg-team-blue/10">
                   Fazer login
                 </Button>
               </Link>
             </div>
           </div>
           <div className="hero-image rounded-lg overflow-hidden shadow-2xl">
             <img
               src="https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
               alt="Dashboard Game Day Nexus"
               className="w-full h-auto object-cover"
             />
           </div>
         </div>
       </section>

       {/* Features */}
       <section id="features" ref={featuresRef} className="py-20 px-6 md:px-12 lg:px-20 bg-gray-50">
         <div className="max-w-7xl mx-auto">
           <motion.div
             initial="hidden"
             whileInView="visible"
             viewport={{ once: true }}
             variants={fadeInUpVariants}
             className="text-center mb-16"
           >
             <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Recursos poderosos</h2>
             <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
               Tudo o que você precisa para gerenciar seu clube de forma eficiente e profissional.
             </p>
           </motion.div>

           <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
             <div className="feature-item p-6 bg-white rounded-xl shadow-md border border-gray-100">
               <div className="w-12 h-12 rounded-full bg-team-blue/10 flex items-center justify-center mb-4">
                 <Users className="h-6 w-6 text-team-blue" />
               </div>
               <h3 className="text-xl font-semibold mb-2">Gestão de Elenco</h3>
               <p className="text-gray-600">Cadastre, monitore e gerencie todos os dados dos jogadores, comissão técnica e funcionários.</p>
             </div>

             <div className="feature-item p-6 bg-white rounded-xl shadow-md border border-gray-100">
               <div className="w-12 h-12 rounded-full bg-team-blue/10 flex items-center justify-center mb-4">
                 <Calendar className="h-6 w-6 text-team-blue" />
               </div>
               <h3 className="text-xl font-semibold mb-2">Partidas e Estatísticas</h3>
               <p className="text-gray-600">Organize jogos, escale o time e registre estatísticas completas de cada partida.</p>
             </div>

             <div className="feature-item p-6 bg-white rounded-xl shadow-md border border-gray-100">
               <div className="w-12 h-12 rounded-full bg-team-blue/10 flex items-center justify-center mb-4">
                 <TrendingUp className="h-6 w-6 text-team-blue" />
               </div>
               <h3 className="text-xl font-semibold mb-2">Treinamentos</h3>
               <p className="text-gray-600">Crie objetivos de treino, acompanhe o progresso e analise o desempenho dos jogadores.</p>
             </div>

             <div className="feature-item p-6 bg-white rounded-xl shadow-md border border-gray-100">
               <div className="w-12 h-12 rounded-full bg-team-blue/10 flex items-center justify-center mb-4">
                 <Shield className="h-6 w-6 text-team-blue" />
               </div>
               <h3 className="text-xl font-semibold mb-2">Departamento Médico</h3>
               <p className="text-gray-600">Acompanhe lesões, tratamentos e recuperação dos atletas com registros médicos detalhados.</p>
             </div>

             <div className="feature-item p-6 bg-white rounded-xl shadow-md border border-gray-100">
               <div className="w-12 h-12 rounded-full bg-team-blue/10 flex items-center justify-center mb-4">
                 <Award className="h-6 w-6 text-team-blue" />
               </div>
               <h3 className="text-xl font-semibold mb-2">Base Juvenil</h3>
               <p className="text-gray-600">Gerencie jovens talentos e acompanhe o desenvolvimento das categorias de base.</p>
             </div>

             <div className="feature-item p-6 bg-white rounded-xl shadow-md border border-gray-100">
               <div className="w-12 h-12 rounded-full bg-team-accent/10 flex items-center justify-center mb-4">
                 <Calendar className="h-6 w-6 text-team-accent" />
               </div>
               <h3 className="text-xl font-semibold mb-2">Agenda & Eventos</h3>
               <p className="text-gray-600">Organize calendário de eventos, reuniões e compromissos de forma integrada.</p>
             </div>
           </div>
         </div>
       </section>

       {/* Testimonials */}
       <section id="testimonials" className="py-20 px-6 md:px-12 lg:px-20 bg-gradient-to-b from-white to-team-blue/5">
         <div className="max-w-7xl mx-auto">
           <motion.div
             initial="hidden"
             whileInView="visible"
             viewport={{ once: true }}
             variants={fadeInUpVariants}
             className="text-center mb-16"
           >
             <h2 className="text-3xl md:text-4xl font-bold text-gray-900">O que nossos clientes dizem</h2>
             <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
               Clubes em todo o Brasil já confiam na plataforma Game Day Nexus para sua gestão.
             </p>
           </motion.div>

           <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
             <motion.div
               initial={{ opacity: 0, y: 30 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.5, delay: 0.1 }}
               viewport={{ once: true }}
               className="bg-white p-6 rounded-xl shadow-md"
             >
               <div className="flex items-center mb-4">
                 <div className="w-12 h-12 rounded-full bg-team-blue flex items-center justify-center text-white font-bold">
                   FC
                 </div>
                 <div className="ml-4">
                   <h4 className="font-semibold">Flamengo FC</h4>
                   <p className="text-sm text-gray-500">Clube Série A</p>
                 </div>
               </div>
               <p className="text-gray-700">
                 "A plataforma revolucionou nossa gestão esportiva. Conseguimos integrar todos os departamentos e ter uma visão completa do clube."
               </p>
             </motion.div>

             <motion.div
               initial={{ opacity: 0, y: 30 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.5, delay: 0.2 }}
               viewport={{ once: true }}
               className="bg-white p-6 rounded-xl shadow-md"
             >
               <div className="flex items-center mb-4">
                 <div className="w-12 h-12 rounded-full bg-team-green flex items-center justify-center text-white font-bold">
                   SC
                 </div>
                 <div className="ml-4">
                   <h4 className="font-semibold">Santos Club</h4>
                   <p className="text-sm text-gray-500">Clube Base</p>
                 </div>
               </div>
               <p className="text-gray-700">
                 "O módulo de categorias de base é fantástico. Conseguimos acompanhar o desenvolvimento dos jovens talentos de forma muito mais eficiente."
               </p>
             </motion.div>

             <motion.div
               initial={{ opacity: 0, y: 30 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.5, delay: 0.3 }}
               viewport={{ once: true }}
               className="bg-white p-6 rounded-xl shadow-md"
             >
               <div className="flex items-center mb-4">
                 <div className="w-12 h-12 rounded-full bg-team-accent flex items-center justify-center text-white font-bold">
                   PF
                 </div>
                 <div className="ml-4">
                   <h4 className="font-semibold">Palmeiras Futebol</h4>
                   <p className="text-sm text-gray-500">Clube Série A</p>
                 </div>
               </div>
               <p className="text-gray-700">
                 "As estatísticas e análises de desempenho nos ajudaram a identificar pontos de melhoria que passavam despercebidos antes."
               </p>
             </motion.div>
           </div>
         </div>
       </section>

       {/* Pricing */}
       <section id="pricing" ref={pricingRef} className="py-20 px-6 md:px-12 lg:px-20 bg-white">
         <div className="max-w-7xl mx-auto">
           <div className="text-center mb-16">
             <h2 className="pricing-title text-3xl md:text-4xl font-bold text-gray-900">Planos que se ajustam ao seu clube</h2>
             <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
               Escolha o plano que melhor atende às necessidades do seu clube, sem compromisso.
             </p>
           </div>

           <div className="grid md:grid-cols-3 gap-8">
             <div className="pricing-card p-6 bg-white rounded-xl shadow-md border border-gray-100 relative">
               <div className="absolute top-0 inset-x-0 h-2 bg-team-blue rounded-t-xl"></div>
               <h3 className="text-xl font-semibold mb-2 mt-4">Básico</h3>
               <div className="mb-4">
                 <span className="text-4xl font-bold">R$99</span>
                 <span className="text-gray-500">/mês</span>
               </div>
               <p className="text-gray-600 mb-6">Ideal para clubes pequenos e equipes em formação.</p>
               <ul className="space-y-3 mb-8">
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-blue mr-2" />
                   <span>Gestão de elenco</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-blue mr-2" />
                   <span>Partidas e estatísticas</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-blue mr-2" />
                   <span>Treinamentos básicos</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-blue mr-2" />
                   <span>1 Temporada</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-blue mr-2" />
                   <span>Até 30 jogadores</span>
                 </li>
               </ul>
               <Button className="w-full bg-white hover:bg-gray-50 text-team-blue border border-team-blue">
                 Começar grátis
               </Button>
             </div>

             <div className="pricing-card p-6 bg-team-blue text-white rounded-xl shadow-lg transform scale-105 relative">
               <div className="absolute top-0 inset-x-0 h-2 bg-team-accent rounded-t-xl"></div>
               <div className="absolute -top-4 right-0 bg-team-accent text-white px-3 py-1 rounded-full text-sm font-medium">
                 Popular
               </div>
               <h3 className="text-xl font-semibold mb-2 mt-4">Profissional</h3>
               <div className="mb-4">
                 <span className="text-4xl font-bold">R$199</span>
                 <span className="text-blue-200">/mês</span>
               </div>
               <p className="text-blue-100 mb-6">Perfeito para clubes em crescimento com necessidades completas.</p>
               <ul className="space-y-3 mb-8">
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-accent mr-2" />
                   <span>Tudo do plano Básico</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-accent mr-2" />
                   <span>Departamento médico</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-accent mr-2" />
                   <span>Gestão financeira</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-accent mr-2" />
                   <span>5 Temporadas</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-accent mr-2" />
                   <span>Até 100 jogadores</span>
                 </li>
               </ul>
               <Button className="w-full bg-team-accent hover:bg-team-accent/90 text-white border-0">
                 Assinar agora
               </Button>
             </div>

             <div className="pricing-card p-6 bg-white rounded-xl shadow-md border border-gray-100 relative">
               <div className="absolute top-0 inset-x-0 h-2 bg-team-green rounded-t-xl"></div>
               <h3 className="text-xl font-semibold mb-2 mt-4">Enterprise</h3>
               <div className="mb-4">
                 <span className="text-4xl font-bold">R$399</span>
                 <span className="text-gray-500">/mês</span>
               </div>
               <p className="text-gray-600 mb-6">Solução completa para clubes profissionais de alto rendimento.</p>
               <ul className="space-y-3 mb-8">
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-green mr-2" />
                   <span>Tudo do plano Profissional</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-green mr-2" />
                   <span>Base juvenil completa</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-green mr-2" />
                   <span>Analytics avançado</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-green mr-2" />
                   <span>Temporadas ilimitadas</span>
                 </li>
                 <li className="flex items-center">
                   <Check className="h-5 w-5 text-team-green mr-2" />
                   <span>Jogadores ilimitados</span>
                 </li>
               </ul>
               <Button className="w-full bg-white hover:bg-gray-50 text-team-green border border-team-green">
                 Contate-nos
               </Button>
             </div>
           </div>
         </div>
       </section>

       {/* Call to action */}
       <section className="py-20 px-6 md:px-12 lg:px-20 bg-team-blue text-white">
         <div className="max-w-5xl mx-auto text-center">
           <motion.h2
             initial={{ opacity: 0, y: 30 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.5 }}
             viewport={{ once: true }}
             className="text-3xl md:text-4xl font-bold mb-6"
           >
             Pronto para transformar a gestão do seu clube?
           </motion.h2>
           <motion.p
             initial={{ opacity: 0, y: 20 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.5, delay: 0.2 }}
             viewport={{ once: true }}
             className="text-xl text-blue-100 mb-10 max-w-3xl mx-auto"
           >
             Junte-se a centenas de clubes que já melhoraram seu desempenho com o Game Day Nexus.
           </motion.p>
           <motion.div
             initial={{ opacity: 0, y: 20 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.5, delay: 0.4 }}
             viewport={{ once: true }}
             className="flex flex-col sm:flex-row gap-4 justify-center"
           >
             <Link to="/">
               <Button size="lg" className="bg-white text-team-blue hover:bg-gray-100 font-medium px-8">
                 Começar agora
               </Button>
             </Link>
             <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/20">
               Agendar demonstração
             </Button>
           </motion.div>
         </div>
       </section>

       {/* Footer */}
       <footer className="bg-gray-900 text-gray-300 py-12 px-6 md:px-12 lg:px-20">
         <div className="max-w-7xl mx-auto">
           <div className="grid md:grid-cols-4 gap-8">
             <div>
               <div className="flex items-center gap-2 mb-4">
                 <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center">
                   <span className="text-team-blue font-bold">GDN</span>
                 </div>
                 <span className="font-bold text-xl text-white">Game Day Nexus</span>
               </div>
               <p className="text-gray-400 mb-4">
                 A plataforma completa de gestão esportiva para clubes de futebol.
               </p>
             </div>

             <div>
               <h3 className="font-semibold text-white mb-4">Produto</h3>
               <ul className="space-y-2">
                 <li><a href="#features" className="hover:text-white transition-colors">Recursos</a></li>
                 <li><a href="#pricing" className="hover:text-white transition-colors">Planos</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">Atualizações</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">FAQ</a></li>
               </ul>
             </div>

             <div>
               <h3 className="font-semibold text-white mb-4">Empresa</h3>
               <ul className="space-y-2">
                 <li><a href="#" className="hover:text-white transition-colors">Sobre nós</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">Contato</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">Termos</a></li>
               </ul>
             </div>

             <div>
               <h3 className="font-semibold text-white mb-4">Contato</h3>
               <ul className="space-y-2">
                 <li className="flex items-center">
                   <span><EMAIL></span>
                 </li>
                 <li className="flex items-center">
                   <span>+55 (11) 3456-7890</span>
                 </li>
               </ul>
             </div>
           </div>

           <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
             <p>&copy; 2025 Game Day Nexus. Todos os direitos reservados.</p>
             <div className="flex gap-4 mt-4 md:mt-0">
               <a href="#" className="hover:text-white transition-colors">Instagram</a>
               <a href="#" className="hover:text-white transition-colors">Twitter</a>
               <a href="#" className="hover:text-white transition-colors">LinkedIn</a>
               <a href="#" className="hover:text-white transition-colors">YouTube</a>
             </div>
           </div>
         </div>
       </footer>
     </div>
   );
 }